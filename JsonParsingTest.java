import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

public class JsonParsingTest {
    
    public static void main(String[] args) {
        String json = "{\n" +
                "    \"tenantId\": \"tenant1\",\n" +
                "    \"filePath\": \"images/test.jpg\",\n" +
                "    \"fileName\": \"test.jpg\",\n" +
                "    \"expireTime\": 1749391445000,\n" +
                "    \"preview\": true,\n" +
                "    \"extraParams\": {\n" +
                "      \"userId\": \"12345\",\n" +
                "      \"source\": \"mobile\",\n" +
                "      \"version\": \"1.0\"\n" +
                "    }\n" +
                "  }";
        
        System.out.println("测试JSON: " + json);
        
        // 测试基本字段提取
        System.out.println("tenantId: " + extractJsonValue(json, "tenantId"));
        System.out.println("filePath: " + extractJsonValue(json, "filePath"));
        System.out.println("expireTime: " + extractJsonValue(json, "expireTime"));
        System.out.println("preview: " + extractJsonValue(json, "preview"));
        
        // 测试extraParams提取
        Map<String, String> extraParams = extractJsonObject(json, "extraParams");
        System.out.println("extraParams: " + extraParams);
    }
    
    private static String extractJsonValue(String json, String fieldName) {
        String pattern = "\"" + fieldName + "\"\\s*:\\s*\"([^\"]+)\"";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(json);
        if (m.find()) {
            return m.group(1);
        }
        
        // 尝试匹配非字符串值（数字、布尔值）
        pattern = "\"" + fieldName + "\"\\s*:\\s*([^,}\\s]+)";
        p = Pattern.compile(pattern);
        m = p.matcher(json);
        if (m.find()) {
            return m.group(1).trim();
        }
        
        return null;
    }
    
    private static Map<String, String> extractJsonObject(String json, String fieldName) {
        Map<String, String> result = new HashMap<>();
        
        // 查找extraParams对象的开始和结束位置
        String pattern = "\"" + fieldName + "\"\\s*:\\s*\\{([^}]+)\\}";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(json);
        
        if (m.find()) {
            String objectContent = m.group(1);
            System.out.println("提取的对象内容: " + objectContent);
            
            // 解析对象内容
            String[] pairs = objectContent.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim().replaceAll("\"", "");
                    String value = keyValue[1].trim().replaceAll("\"", "");
                    result.put(key, value);
                }
            }
        }
        
        return result;
    }
}
