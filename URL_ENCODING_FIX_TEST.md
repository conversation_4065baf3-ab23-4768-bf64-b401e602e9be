# URL编码问题修复验证

## 问题描述
生成的签名URL中包含`+`号，在URL传输过程中被解码为空格，导致签名验证失败。

## 修复方案

### 1. URL编码修复
在生成URL时对签名进行编码：
```java
// 查询参数 - 对签名进行URL编码
urlBuilder.append("?signature=").append(urlEncode(signature));
```

### 2. URL解码修复
在解析请求时对签名进行解码：
```java
// 设置签名（需要URL解码）
String signature = request.getParameter("signature");
if (signature != null) {
    fileRequest.setSignature(urlDecode(signature));
}
```

### 3. 编码规则
- `+` → `%2B`
- `/` → `%2F`  
- `=` → `%3D`
- 空格 → `%20`

## 测试验证

### 原始签名
```
C+fIK6PRw95JZRO6UlRHSvfpk/v1VJIbjvByxvI2WxA=
```

### URL编码后
```
C%2BfIK6PRw95JZRO6UlRHSvfpk%2Fv1VJIbjvByxvI2WxA%3D
```

### 解码后应该恢复原始签名
```
C+fIK6PRw95JZRO6UlRHSvfpk/v1VJIbjvByxvI2WxA=
```

## 预期结果
修复后，生成的URL应该是：
```
/sxr/preview/tenant1/images/test.jpg?signature=C%2BfIK6PRw95JZRO6UlRHSvfpk%2Fv1VJIbjvByxvI2WxA%3D&expire=1749391445000
```

访问时签名验证应该成功。
