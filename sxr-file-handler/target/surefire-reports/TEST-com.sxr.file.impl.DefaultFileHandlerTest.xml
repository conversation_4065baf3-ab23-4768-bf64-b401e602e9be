<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.sxr.file.impl.DefaultFileHandlerTest" time="0.038" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="socksProxyHost" value="127.0.0.1"/>
    <property name="http.proxyHost" value="127.0.0.1"/>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler/target/test-classes:/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:"/>
    <property name="https.proxyPort" value="7890"/>
    <property name="java.vm.vendor" value="Amazon.com Inc."/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://aws.amazon.com/corretto/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.13/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler/target/surefire/surefirebooter-20250607095645426_6.jar /Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler/target/surefire 2025-06-07T09-56-34_430-jvmRun1 surefire-20250607095645426_4tmp surefire_1-20250607095645426_5tmp"/>
    <property name="http.nonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler/target/test-classes:/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2024-10-15"/>
    <property name="java.home" value="/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-17.0.13/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="https.proxyHost" value="127.0.0.1"/>
    <property name="basedir" value="/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler/target/surefire/surefirebooter-20250607095645426_6.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="java.runtime.version" value="17.0.13+11-LTS"/>
    <property name="user.name" value="wulinghui"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Corretto-**********.1"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/corretto/corretto-17/issues/"/>
    <property name="java.io.tmpdir" value="/var/folders/tr/19s983_s41v5zkfd_pf84bj00000gn/T/"/>
    <property name="java.version" value="17.0.13"/>
    <property name="user.dir" value="/Users/<USER>/IdeaProjects/sxrFileSignature/sxr-file-handler"/>
    <property name="os.arch" value="aarch64"/>
    <property name="socksProxyPort" value="7891"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Amazon.com Inc."/>
    <property name="java.vm.version" value="17.0.13+11-LTS"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="java.class.version" value="61.0"/>
    <property name="http.proxyPort" value="7890"/>
  </properties>
  <testcase name="testHandleDownloadWithNonExistentFile" classname="com.sxr.file.impl.DefaultFileHandlerTest" time="0.002"/>
  <testcase name="testSupportFormat" classname="com.sxr.file.impl.DefaultFileHandlerTest" time="0.0"/>
  <testcase name="testHandleDownloadWithInvalidRequest" classname="com.sxr.file.impl.DefaultFileHandlerTest" time="0.0"/>
  <testcase name="testHandlePreviewWithInvalidRequest" classname="com.sxr.file.impl.DefaultFileHandlerTest" time="0.0"/>
  <testcase name="testGetContentType" classname="com.sxr.file.impl.DefaultFileHandlerTest" time="0.0"/>
</testsuite>