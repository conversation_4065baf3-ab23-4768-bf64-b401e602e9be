package com.sxr.file.impl;

import com.sxr.file.AbstractFileHandler;
import com.sxr.file.FileRequest;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

/**
 * 默认文件处理器实现
 * 支持本地文件系统的文件下载和预览
 * 继承AbstractFileHandler，专注于文件读写逻辑
 * 零第三方依赖，仅使用JDK8标准库
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DefaultFileHandler extends AbstractFileHandler {

    @Override
    protected FileInfo getFileInfo(FileRequest request) {
        try {
            // 构建完整文件路径
            String fullPath = buildFullPath(request);
            File file = new File(fullPath);

            // 检查文件是否存在
            if (!file.exists() || !file.isFile()) {
                return null;
            }

            // 检查文件是否可读
            if (!file.canRead()) {
                return null;
            }

            // 构建文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(request.getFileName() != null ? request.getFileName() : file.getName());
            fileInfo.setFileSize(file.length());
            fileInfo.setLastModified(file.lastModified());
            fileInfo.setFilePath(fullPath);

            return fileInfo;

        } catch (Exception e) {
            System.err.println("Failed to get file info: " + e.getMessage());
            return null;
        }
    }

    @Override
    protected InputStream readFileContent(FileRequest request, FileInfo fileInfo) {
        try {
            File file = new File(fileInfo.getFilePath());
            return new FileInputStream(file);
        } catch (FileNotFoundException e) {
            System.err.println("File not found: " + e.getMessage());
            return null;
        } catch (Exception e) {
            System.err.println("Failed to read file content: " + e.getMessage());
            return null;
        }
    }

    /**
     * 构建完整文件路径
     */
    private String buildFullPath(FileRequest request) {
        // 简化实现，实际项目中需要考虑安全性（路径遍历攻击等）
        String basePath = getBasePath(request.getTenantId());
        return basePath + File.separator + request.getFilePath();
    }

    /**
     * 获取租户的基础路径
     */
    private String getBasePath(String tenantId) {
        // 简化实现，实际项目中从配置中获取
        String baseDir = System.getProperty("sxr.file.base.dir", "./files");
        if (tenantId != null) {
            return baseDir + File.separator + tenantId;
        }
        return baseDir;
    }
}
