package com.sxr.file.impl;

import com.sxr.file.FileHandler;
import com.sxr.file.FileRequest;
import com.sxr.file.FileResponse;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 默认文件处理器实现
 * 支持本地文件系统的文件下载和预览
 * 零第三方依赖，仅使用JDK8标准库
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DefaultFileHandler implements FileHandler {
    
    /** 文件扩展名到MIME类型的映射 */
    private static final Map<String, String> MIME_TYPE_MAP = new HashMap<>();
    
    /** 支持预览的文件格式 */
    private static final Map<String, Boolean> PREVIEW_SUPPORT_MAP = new HashMap<>();
    
    static {
        // 初始化MIME类型映射
        initMimeTypeMap();
        // 初始化预览支持映射
        initPreviewSupportMap();
    }
    
    @Override
    public FileResponse handleDownload(FileRequest request) {
        return handleFileRequest(request, false);
    }
    
    @Override
    public FileResponse handlePreview(FileRequest request) {
        return handleFileRequest(request, true);
    }
    
    @Override
    public boolean supportFormat(String extension) {
        if (extension == null) {
            return false;
        }
        return MIME_TYPE_MAP.containsKey(extension.toLowerCase());
    }
    
    @Override
    public String getContentType(String extension) {
        if (extension == null) {
            return "application/octet-stream";
        }
        return MIME_TYPE_MAP.getOrDefault(extension.toLowerCase(), "application/octet-stream");
    }
    
    /**
     * 处理文件请求的通用方法
     */
    private FileResponse handleFileRequest(FileRequest request, boolean isPreview) {
        if (request == null || request.getFilePath() == null) {
            return FileResponse.error(400, "Invalid file request");
        }
        
        try {
            // 构建完整文件路径
            String fullPath = buildFullPath(request);
            File file = new File(fullPath);
            
            // 检查文件是否存在
            System.out.println(file.getAbsoluteFile());
            if (!file.exists() || !file.isFile()) {
                return FileResponse.notFound();
            }
            
            // 检查文件是否可读
            if (!file.canRead()) {
                return FileResponse.forbidden();
            }
            
            // 创建成功响应
            FileResponse response = FileResponse.success();
            
            // 设置文件流
            FileInputStream inputStream = new FileInputStream(file);
            response.setInputStream(inputStream);
            response.setContentLength(file.length());
            
            // 设置Content-Type
            String extension = request.getFileExtension();
            String contentType = getContentType(extension);
            response.setContentType(contentType);
            
            // 设置响应头
            setResponseHeaders(response, request, file, isPreview);
            
            return response;
            
        } catch (FileNotFoundException e) {
            return FileResponse.notFound();
        } catch (SecurityException e) {
            return FileResponse.forbidden();
        } catch (Exception e) {
            return FileResponse.internalError("Failed to process file: " + e.getMessage());
        }
    }
    
    /**
     * 构建完整文件路径
     */
    private String buildFullPath(FileRequest request) {
        // 简化实现，实际项目中需要考虑安全性（路径遍历攻击等）
        String basePath = getBasePath(request.getTenantId());
        return basePath + File.separator + request.getFilePath();
    }
    
    /**
     * 获取租户的基础路径
     */
    private String getBasePath(String tenantId) {
        // 简化实现，实际项目中从配置中获取
        String baseDir = System.getProperty("sxr.file.base.dir", "./files");
        if (tenantId != null) {
            return baseDir + File.separator + tenantId;
        }
        return baseDir;
    }
    
    /**
     * 设置响应头
     */
    private void setResponseHeaders(FileResponse response, FileRequest request, File file, boolean isPreview) {
        String extension = request.getFileExtension();
        
        // 设置缓存控制
        response.addHeader("Cache-Control", "private, max-age=3600");
        response.addHeader("Last-Modified", String.valueOf(file.lastModified()));
        
        // 根据预览模式设置Content-Disposition
        if (isPreview && isPreviewSupported(extension)) {
            response.addHeader("Content-Disposition", "inline; filename=\"" + request.getFileName() + "\"");
        } else {
            response.addHeader("Content-Disposition", "attachment; filename=\"" + request.getFileName() + "\"");
        }
        
        // 设置安全头
        response.addHeader("X-Content-Type-Options", "nosniff");
        response.addHeader("X-Frame-Options", "SAMEORIGIN");
        
        // 处理Range请求（断点续传）
        if (request.getRangeHeader() != null) {
            // TODO: 实现Range请求处理
            // 这里需要解析Range头并返回部分内容
        }
    }
    
    /**
     * 检查文件格式是否支持预览
     */
    private boolean isPreviewSupported(String extension) {
        if (extension == null) {
            return false;
        }
        return PREVIEW_SUPPORT_MAP.getOrDefault(extension.toLowerCase(), false);
    }
    
    /**
     * 初始化MIME类型映射
     */
    private static void initMimeTypeMap() {
        // 图片格式
        MIME_TYPE_MAP.put("jpg", "image/jpeg");
        MIME_TYPE_MAP.put("jpeg", "image/jpeg");
        MIME_TYPE_MAP.put("png", "image/png");
        MIME_TYPE_MAP.put("gif", "image/gif");
        MIME_TYPE_MAP.put("webp", "image/webp");
        MIME_TYPE_MAP.put("bmp", "image/bmp");
        MIME_TYPE_MAP.put("ico", "image/x-icon");
        
        // 视频格式
        MIME_TYPE_MAP.put("mp4", "video/mp4");
        MIME_TYPE_MAP.put("avi", "video/x-msvideo");
        MIME_TYPE_MAP.put("mov", "video/quicktime");
        MIME_TYPE_MAP.put("wmv", "video/x-ms-wmv");
        MIME_TYPE_MAP.put("flv", "video/x-flv");
        MIME_TYPE_MAP.put("webm", "video/webm");
        
        // 音频格式
        MIME_TYPE_MAP.put("mp3", "audio/mpeg");
        MIME_TYPE_MAP.put("wav", "audio/wav");
        MIME_TYPE_MAP.put("flac", "audio/flac");
        MIME_TYPE_MAP.put("aac", "audio/aac");
        MIME_TYPE_MAP.put("ogg", "audio/ogg");
        
        // 文档格式
        MIME_TYPE_MAP.put("pdf", "application/pdf");
        MIME_TYPE_MAP.put("doc", "application/msword");
        MIME_TYPE_MAP.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_TYPE_MAP.put("xls", "application/vnd.ms-excel");
        MIME_TYPE_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        MIME_TYPE_MAP.put("ppt", "application/vnd.ms-powerpoint");
        MIME_TYPE_MAP.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        
        // 文本格式
        MIME_TYPE_MAP.put("txt", "text/plain");
        MIME_TYPE_MAP.put("html", "text/html");
        MIME_TYPE_MAP.put("css", "text/css");
        MIME_TYPE_MAP.put("js", "application/javascript");
        MIME_TYPE_MAP.put("json", "application/json");
        MIME_TYPE_MAP.put("xml", "application/xml");
        
        // 压缩格式
        MIME_TYPE_MAP.put("zip", "application/zip");
        MIME_TYPE_MAP.put("rar", "application/x-rar-compressed");
        MIME_TYPE_MAP.put("7z", "application/x-7z-compressed");
        MIME_TYPE_MAP.put("tar", "application/x-tar");
        MIME_TYPE_MAP.put("gz", "application/gzip");
    }
    
    /**
     * 初始化预览支持映射
     */
    private static void initPreviewSupportMap() {
        // 图片支持预览
        PREVIEW_SUPPORT_MAP.put("jpg", true);
        PREVIEW_SUPPORT_MAP.put("jpeg", true);
        PREVIEW_SUPPORT_MAP.put("png", true);
        PREVIEW_SUPPORT_MAP.put("gif", true);
        PREVIEW_SUPPORT_MAP.put("webp", true);
        PREVIEW_SUPPORT_MAP.put("bmp", true);
        
        // 视频支持预览
        PREVIEW_SUPPORT_MAP.put("mp4", true);
        PREVIEW_SUPPORT_MAP.put("webm", true);
        
        // 音频支持预览
        PREVIEW_SUPPORT_MAP.put("mp3", true);
        PREVIEW_SUPPORT_MAP.put("wav", true);
        PREVIEW_SUPPORT_MAP.put("ogg", true);
        
        // PDF支持预览
        PREVIEW_SUPPORT_MAP.put("pdf", true);
        
        // 文本支持预览
        PREVIEW_SUPPORT_MAP.put("txt", true);
        PREVIEW_SUPPORT_MAP.put("html", true);
        PREVIEW_SUPPORT_MAP.put("css", true);
        PREVIEW_SUPPORT_MAP.put("js", true);
        PREVIEW_SUPPORT_MAP.put("json", true);
        PREVIEW_SUPPORT_MAP.put("xml", true);
    }
}
