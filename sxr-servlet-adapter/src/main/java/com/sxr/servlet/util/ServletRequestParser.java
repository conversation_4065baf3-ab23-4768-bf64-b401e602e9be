package com.sxr.servlet.util;

import com.sxr.integration.SecureFileRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Servlet请求解析工具
 * 将HttpServletRequest转换为业务请求对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ServletRequestParser {
    
    /**
     * 解析安全文件请求
     * 
     * @param request HTTP请求
     * @param isPreview 是否为预览模式
     * @return 安全文件请求对象
     */
    public SecureFileRequest parseSecureFileRequest(HttpServletRequest request, boolean isPreview) {
        SecureFileRequest fileRequest = new SecureFileRequest();
        
        // 解析路径信息
        String pathInfo = extractPathInfo(request, isPreview);
        parseTenantAndFilePath(pathInfo, fileRequest);
        
        // 设置基本参数
        fileRequest.setPreview(isPreview);
        fileRequest.setSignature(request.getParameter("signature"));
        fileRequest.setClientIp(getClientIp(request));
        fileRequest.setUserAgent(request.getHeader("User-Agent"));
        fileRequest.setRangeHeader(request.getHeader("Range"));
        
        // 设置过期时间
        String expireParam = request.getParameter("expire");
        if (expireParam != null && !expireParam.trim().isEmpty()) {
            try {
                fileRequest.setExpireTime(Long.parseLong(expireParam));
            } catch (NumberFormatException e) {
                // 忽略无效的过期时间参数
            }
        }
        
        // 设置访问限制
        String limitParam = request.getParameter("limit");
        if (limitParam != null && !limitParam.trim().isEmpty()) {
            try {
                fileRequest.setAccessLimit(Integer.parseInt(limitParam));
            } catch (NumberFormatException e) {
                // 忽略无效的限制参数
            }
        }
        
        // 设置请求头
        fileRequest.setHeaders(extractHeaders(request));
        
        return fileRequest;
    }
    
    /**
     * 解析JSON请求
     * 
     * @param request HTTP请求
     * @return 安全文件请求对象
     * @throws IOException IO异常
     */
    public SecureFileRequest parseJsonRequest(HttpServletRequest request) throws IOException {
        // 读取请求体
        StringBuilder jsonBuilder = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuilder.append(line);
            }
        }
        
        String jsonBody = jsonBuilder.toString();
        
        // 简单的JSON解析（生产环境建议使用专业的JSON库）
        return parseJsonToSecureFileRequest(jsonBody);
    }
    
    /**
     * 提取路径信息
     */
    private String extractPathInfo(HttpServletRequest request, boolean isPreview) {
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        
        // 移除上下文路径
        if (contextPath != null && !contextPath.isEmpty()) {
            requestURI = requestURI.substring(contextPath.length());
        }
        
        // 移除servlet路径前缀
        String prefix = isPreview ? "/sxr/preview/" : "/sxr/download/";
        if (requestURI.startsWith(prefix)) {
            return requestURI.substring(prefix.length());
        }
        
        return requestURI;
    }
    
    /**
     * 解析租户ID和文件路径
     */
    private void parseTenantAndFilePath(String pathInfo, SecureFileRequest fileRequest) {
        if (pathInfo == null || pathInfo.trim().isEmpty()) {
            return;
        }
        
        // 移除开头的斜杠
        if (pathInfo.startsWith("/")) {
            pathInfo = pathInfo.substring(1);
        }
        
        // 简单的路径解析：/tenantId/filePath
        String[] parts = pathInfo.split("/", 2);
        if (parts.length >= 2) {
            fileRequest.setTenantId(parts[0]);
            fileRequest.setFilePath(parts[1]);
        } else if (parts.length == 1) {
            fileRequest.setFilePath(parts[0]);
        }
        
        // 设置文件名
        String filePath = fileRequest.getFilePath();
        if (filePath != null && !filePath.trim().isEmpty()) {
            int lastSlashIndex = filePath.lastIndexOf('/');
            if (lastSlashIndex >= 0 && lastSlashIndex < filePath.length() - 1) {
                fileRequest.setFileName(filePath.substring(lastSlashIndex + 1));
            } else {
                fileRequest.setFileName(filePath);
            }
        }
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 提取请求头
     */
    private Map<String, String> extractHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
        }
        
        return headers;
    }
    
    /**
     * 简单的JSON解析（生产环境建议使用Jackson或Gson）
     */
    private SecureFileRequest parseJsonToSecureFileRequest(String json) {
        SecureFileRequest request = new SecureFileRequest();
        
        if (json == null || json.trim().isEmpty()) {
            return request;
        }
        
        // 移除花括号
        json = json.trim();
        if (json.startsWith("{")) {
            json = json.substring(1);
        }
        if (json.endsWith("}")) {
            json = json.substring(0, json.length() - 1);
        }
        
        // 简单解析键值对
        String[] pairs = json.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":", 2);
            if (keyValue.length == 2) {
                String key = keyValue[0].trim().replaceAll("\"", "");
                String value = keyValue[1].trim().replaceAll("\"", "");
                
                switch (key) {
                    case "tenantId":
                        request.setTenantId(value);
                        break;
                    case "filePath":
                        request.setFilePath(value);
                        break;
                    case "fileName":
                        request.setFileName(value);
                        break;
                    case "preview":
                        request.setPreview(Boolean.parseBoolean(value));
                        break;
                    case "expireTime":
                        try {
                            request.setExpireTime(Long.parseLong(value));
                        } catch (NumberFormatException e) {
                            // 忽略无效值
                        }
                        break;
                    case "accessLimit":
                        try {
                            request.setAccessLimit(Integer.parseInt(value));
                        } catch (NumberFormatException e) {
                            // 忽略无效值
                        }
                        break;
                }
            }
        }
        
        return request;
    }
}
