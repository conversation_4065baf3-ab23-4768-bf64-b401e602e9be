package com.sxr.servlet;

import com.sxr.servlet.impl.DefaultServletFileService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 安全文件Servlet
 * 提供基于Servlet API的文件访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SecureFileServlet extends HttpServlet {
    
    private ServletFileService servletFileService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        
        // 从ServletContext获取服务实例
        // 在实际应用中，这里应该通过依赖注入或服务定位器获取
        this.servletFileService = (ServletFileService) getServletContext()
                .getAttribute("servletFileService");
        
        if (this.servletFileService == null) {
            throw new ServletException("ServletFileService not found in ServletContext");
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Path not found");
                return;
            }
            
            if (pathInfo.startsWith("/download/")) {
                // 文件下载
                servletFileService.handleSecureDownload(request, response);
            } else if (pathInfo.startsWith("/preview/")) {
                // 文件预览
                servletFileService.handleSecurePreview(request, response);
            } else if (pathInfo.equals("/health")) {
                // 健康检查
                servletFileService.healthCheck(request, response);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Path not found");
            }
            
        } catch (Exception e) {
            handleError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                       "Internal server error: " + e.getMessage());
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/generate-url".equals(pathInfo)) {
                // 生成安全URL
                String jsonResponse = servletFileService.generateSecureUrl(request);
                
                response.setContentType("application/json;charset=UTF-8");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write(jsonResponse);
                response.getWriter().flush();
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Path not found");
            }
            
        } catch (Exception e) {
            handleError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                       "Internal server error: " + e.getMessage());
        }
    }
    
    @Override
    protected void doOptions(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 处理CORS预检请求
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setStatus(HttpServletResponse.SC_OK);
    }
    
    /**
     * 处理错误响应
     */
    private void handleError(HttpServletResponse response, int statusCode, String message) 
            throws IOException {
        
        response.setStatus(statusCode);
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        String errorJson = "{\"success\":false,\"error\":\"" + escapeJson(message) + "\"}";
        response.getWriter().write(errorJson);
        response.getWriter().flush();
    }
    
    /**
     * JSON字符串转义
     */
    private String escapeJson(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\"", "\\\"")
                  .replace("\\", "\\\\")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
