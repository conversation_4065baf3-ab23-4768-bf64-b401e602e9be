package com.sxr.signature;

import java.util.Map;

/**
 * 签名请求参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SignRequest {

    /** 租户ID */
    private String tenantId;

    /** 文件路径 */
    private String filePath;

    /** 过期时间戳(毫秒) */
    private Long expireTime;

    /** 访问次数限制 */
    private Integer accessLimit;

    /** 客户端IP - 仅用于日志记录，不参与签名计算 */
    private String clientIp;

    /** 扩展参数 */
    private Map<String, String> extraParams;

    /** 签名密钥 */
    private String secretKey;

    public SignRequest() {
    }

    public SignRequest(String tenantId, String filePath) {
        this.tenantId = tenantId;
        this.filePath = filePath;
    }

    // Getter and Setter methods
    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getAccessLimit() {
        return accessLimit;
    }

    public void setAccessLimit(Integer accessLimit) {
        this.accessLimit = accessLimit;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public Map<String, String> getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(Map<String, String> extraParams) {
        this.extraParams = extraParams;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    @Override
    public String toString() {
        return "SignRequest{" +
                "tenantId='" + tenantId + '\'' +
                ", filePath='" + filePath + '\'' +
                ", expireTime=" + expireTime +
                ", accessLimit=" + accessLimit +
                ", clientIp='" + clientIp + '\'' +
                ", extraParams=" + extraParams +
                ", secretKey='***'" +
                '}';
    }
}
