package com.sxr.signature.impl;

import com.sxr.signature.SignRequest;
import com.sxr.signature.SignatureService;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Map;
import java.util.TreeMap;

/**
 * 基于HMAC-SHA256的签名服务实现
 * 零第三方依赖，仅使用JDK8标准库
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class HmacSignatureService implements SignatureService {

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String RSA = "RSA";
    private static final int RSA_KEY_SIZE = 2048;

    @Override
    public String generateSignature(SignRequest request) {
        if (request == null || request.getSecretKey() == null) {
            throw new IllegalArgumentException("SignRequest and secretKey cannot be null");
        }

        try {
            // 构建待签名字符串
            String stringToSign = buildStringToSign(request);

            // 使用HMAC-SHA256生成签名
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                request.getSecretKey().getBytes(StandardCharsets.UTF_8),
                HMAC_SHA256
            );
            mac.init(secretKeySpec);

            byte[] signatureBytes = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signatureBytes);

        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }

    @Override
    public boolean verifySignature(String url, String signature) {
        if (url == null || signature == null) {
            return false;
        }

        try {
            // 从URL中解析参数重新构建SignRequest
            SignRequest request = parseUrlToSignRequest(url);
            if (request == null) {
                return false;
            }

            // 重新生成签名进行比较
            String expectedSignature = generateSignature(request);
            return signature.equals(expectedSignature);

        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public KeyPair generateKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA);
            keyPairGenerator.initialize(RSA_KEY_SIZE, new SecureRandom());
            return keyPairGenerator.generateKeyPair();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to generate key pair", e);
        }
    }

    @Override
    public boolean isExpired(SignRequest request) {
        if (request == null || request.getExpireTime() == null) {
            return false;
        }

        return System.currentTimeMillis() > request.getExpireTime();
    }

    /**
     * 构建待签名字符串
     * 按照参数名字典序排列，确保签名一致性
     */
    private String buildStringToSign(SignRequest request) {
        TreeMap<String, String> params = new TreeMap<>();

        // 添加基础参数
        if (request.getTenantId() != null) {
            params.put("tenantId", request.getTenantId());
        }
        if (request.getFilePath() != null) {
            params.put("filePath", request.getFilePath());
        }
        if (request.getExpireTime() != null) {
            params.put("expireTime", request.getExpireTime().toString());
        }
        if (request.getAccessLimit() != null) {
            params.put("accessLimit", request.getAccessLimit().toString());
        }
        // 注意：clientIp不参与签名计算，避免用户切换网络时签名失效

        // 添加扩展参数
        if (request.getExtraParams() != null) {
            params.putAll(request.getExtraParams());
        }

        // 构建签名字符串
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (stringBuilder.length() > 0) {
                stringBuilder.append("&");
            }
            stringBuilder.append(entry.getKey()).append("=").append(entry.getValue());
        }

        return stringBuilder.toString();
    }

    /**
     * 从URL解析参数构建SignRequest
     * 简化实现，实际项目中需要更完善的URL解析
     */
    private SignRequest parseUrlToSignRequest(String url) {
        // TODO: 实现URL参数解析逻辑
        // 这里需要根据实际的URL格式来解析参数
        // 暂时返回null，在集成测试时完善
        return null;
    }
}
