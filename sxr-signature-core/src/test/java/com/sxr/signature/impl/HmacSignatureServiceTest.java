package com.sxr.signature.impl;

import com.sxr.signature.SignRequest;
import com.sxr.signature.SignatureService;
import org.junit.Before;
import org.junit.Test;

import java.security.KeyPair;

import static org.junit.Assert.*;

/**
 * HMAC签名服务测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class HmacSignatureServiceTest {
    
    private SignatureService signatureService;
    
    @Before
    public void setUp() {
        signatureService = new HmacSignatureService();
    }
    
    @Test
    public void testGenerateSignature() {
        // 准备测试数据
        SignRequest request = new SignRequest();
        request.setTenantId("test-tenant");
        request.setFilePath("test/file.jpg");
        request.setExpireTime(System.currentTimeMillis() + 3600000); // 1小时后过期
        request.setSecretKey("test-secret-key");
        
        // 生成签名
        String signature = signatureService.generateSignature(request);
        
        // 验证结果
        assertNotNull("签名不能为空", signature);
        assertTrue("签名长度应该大于0", signature.length() > 0);
        
        // 相同参数应该生成相同签名
        String signature2 = signatureService.generateSignature(request);
        assertEquals("相同参数应该生成相同签名", signature, signature2);
    }
    
    @Test
    public void testGenerateKeyPair() {
        // 生成密钥对
        KeyPair keyPair = signatureService.generateKeyPair();
        
        // 验证结果
        assertNotNull("密钥对不能为空", keyPair);
        assertNotNull("公钥不能为空", keyPair.getPublic());
        assertNotNull("私钥不能为空", keyPair.getPrivate());
        assertEquals("应该是RSA算法", "RSA", keyPair.getPublic().getAlgorithm());
    }
    
    @Test
    public void testIsExpired() {
        SignRequest request = new SignRequest();
        
        // 测试未设置过期时间
        assertFalse("未设置过期时间应该返回false", signatureService.isExpired(request));
        
        // 测试未过期
        request.setExpireTime(System.currentTimeMillis() + 3600000); // 1小时后
        assertFalse("未过期应该返回false", signatureService.isExpired(request));
        
        // 测试已过期
        request.setExpireTime(System.currentTimeMillis() - 1000); // 1秒前
        assertTrue("已过期应该返回true", signatureService.isExpired(request));
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateSignatureWithNullRequest() {
        signatureService.generateSignature(null);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateSignatureWithNullSecretKey() {
        SignRequest request = new SignRequest();
        request.setTenantId("test-tenant");
        request.setFilePath("test/file.jpg");
        // 不设置secretKey
        signatureService.generateSignature(request);
    }
}
