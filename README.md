# sxrFileSignature

#### 介绍
文件签名保护系统，80%+ 的安全
业务场景	                                           建议机制
普通网站、视频教程、社交短视频	   URL 签名 + AES-128 HLS 加密
高价内容、影视、在线课程平台         DRM（Shaka + Widevine）
学术内部资料、安全敏感视频	           DRM + 限设备 + 水印追踪

专注于文件安全的下载/预览.



#### 痛点
1. 文件默认都是直接下载 
> 针对不同文件格式,设置浏览器可以直接预览的响应头.
> 还有视频流式处理等的原生支持.  

2. 统一实现文件url签名实现,并提供sdk


#### 与DRM系统不同
- DRM的播放器是配套的,编码复杂性高。而我跟原生支持.

#### 与文件预览系统不同
- 文件预览系统内部依赖各种js的插件做的内容格式的转化适配。 而我仅做响应头的适配.

#### 与Web服务器不同
- web服务器一般都是下载,预览需要配置,同时需要安全插件配合。 而我默认解决这2个需求。

#### 架构组件设计
用户端（Web/Mobile 播放器）
     │
     └──▶ NGINX 代理（高性能代理,https处理）
               │
               ├──▶ sxrFileSignature切片1
               ├──▶ sxrFileSignature切片2
               └──▶ sxrFileSignature域名1
               └──▶ sxrFileSignature域名2
                           │
                           └──▶ 存储系统（对象存储 / 本地或 S3）  
                                   │
                                   └──▶ HLS/m3u8 加密模块（AES-128 or SAMPLE-AES）
                                   └──▶ 图片
                                   └──▶ MP4

ps: 
- 存储系统如果是分布式的话一定走内网，把他们当作切片来使用
- 或者使用不同的域名单机结构.


#### 软件架构
Spring-boot 3.x 




#### 安装教程

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
