# sxrFileSignature

#### 介绍
文件签名保护系统，80%+ 的安全
业务场景	                                           建议机制
普通网站、视频教程、社交短视频	   URL 签名 + AES-128 HLS 加密
高价内容、影视、在线课程平台         DRM（Shaka + Widevine）
学术内部资料、安全敏感视频	           DRM + 限设备 + 水印追踪

专注于文件安全的下载/预览.



#### 痛点
1. 文件默认都是直接下载
> 针对不同文件格式,设置浏览器可以直接预览的响应头.
> 还有视频流式处理等的原生支持.

2. 统一实现文件url签名实现,并提供sdk


#### 与DRM系统不同
- DRM的播放器是配套的,编码复杂性高。而我跟原生支持.

#### 与文件预览系统不同
- 文件预览系统内部依赖各种js的插件做的内容格式的转化适配。 而我仅做响应头的适配.

#### 与Web服务器不同
- web服务器一般都是下载,预览需要配置,同时需要安全插件配合。 而我默认解决这2个需求。

#### 架构组件设计
用户端（Web/Mobile 播放器）
     │
     └──▶ NGINX 代理（高性能代理,https处理）
               │
               ├──▶ sxrFileSignature切片1
               ├──▶ sxrFileSignature切片2
               └──▶ sxrFileSignature域名1
               └──▶ sxrFileSignature域名2
                           │
                           └──▶ 存储系统（对象存储 / 本地或 S3）
                                   │
                                   └──▶ HLS/m3u8 加密模块（AES-128 or SAMPLE-AES）
                                   └──▶ 图片
                                   └──▶ MP4

ps:
- 存储系统如果是分布式的话一定走内网，把他们当作切片来使用
- 或者使用不同的域名单机结构.


#### 软件架构

本系统采用多层架构设计，各层职责分离，支持插拔式扩展，兼容JDK8环境。

##### 整体架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                    多租户路由层                                    │
│  域名路由: tenant1.domain.com → Tenant1                         │
│  前缀路由: /tenant1/xxx → Tenant1, /tenant2/xxx → Tenant2       │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                 Spring Boot 3.x 容器启动层                       │
│  - 应用启动入口                                                   │
│  - 配置管理                                                      │
│  - 健康检查                                                      │
│  - 监控指标                                                      │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      整合模块层                                   │
│  - 签名验证 + 文件下载/预览组合                                    │
│  - 流量控制插件                                                   │
│  - 访问日志记录                                                   │
│  - 异常处理统一                                                   │
│  - 多租户配置管理                                                 │
└─────────────────────────────────────────────────────────────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   签名模块     │    │  下载/预览模块   │    │   扩展插件模块   │
│               │    │                 │    │                 │
│ - URL签名生成  │    │ - 文件下载      │    │ - 限流插件      │
│ - 签名验证    │    │ - 文件预览      │    │ - 缓存插件      │
│ - 公私钥管理  │    │ - 响应头适配    │    │ - 日志插件      │
│ - 时效控制    │    │ - 流式处理      │    │ - 监控插件      │
│               │    │ - 格式识别      │    │                 │
└───────────────┘    └─────────────────┘    └─────────────────┘
```

##### 分层详细设计

###### 1. 签名模块 (sxr-signature-core)
**职责**: 独立的URL签名生成和验证模块
- **签名算法**: 支持HMAC-SHA256、RSA等多种签名算法
- **密钥管理**: 公私钥对生成、存储、轮换
- **时效控制**: 支持过期时间、访问次数限制
- **参数验证**: URL参数完整性校验
- **多租户**: 每个租户独立的密钥空间
- **网络友好**: IP地址不参与签名，支持用户网络切换

**核心接口**:
```java
public interface SignatureService {
    String generateSignature(SignRequest request);
    boolean verifySignature(String url, String signature);
    KeyPair generateKeyPair();
}
```

###### 2. 下载/预览模块 (sxr-file-handler)
**职责**: 独立的文件处理和响应模块
- **文件下载**: 支持断点续传、范围请求
- **文件预览**: 根据文件扩展名设置合适的Content-Type和响应头
- **流式处理**: 大文件分块传输，内存友好
- **格式支持**:
  - 图片: jpg, png, gif, webp (inline预览)
  - 视频: mp4, avi, mov (inline预览/下载)
  - 文档: pdf (inline预览), doc, docx (下载)
  - 音频: mp3, wav, flac (inline预览)

**核心接口**:
```java
public interface FileHandler {
    FileResponse handleDownload(FileRequest request);
    FileResponse handlePreview(FileRequest request);
    boolean supportFormat(String extension);
}
```

###### 3. 整合模块 (sxr-integration)
**职责**: 组合签名和文件处理，提供完整业务功能
- **请求路由**: 根据URL路径分发到对应处理器
- **签名集成**: 先验证签名，再处理文件请求
- **插件管理**: 支持流量控制、缓存、监控等插件
- **多租户**: 租户隔离和配置管理
- **异常处理**: 统一的错误处理和响应格式

**插件扩展点**:
```java
public interface RequestPlugin {
    boolean preHandle(HttpRequest request, TenantContext context);
    void postHandle(HttpRequest request, HttpResponse response);
}
```

###### 4. Spring Boot 容器层 (sxr-spring-boot-starter)
**职责**: 基于Spring Boot 3.x的快速启动和配置
- **自动配置**: 基于条件的Bean自动装配
- **配置属性**: 外部化配置支持
- **健康检查**: Actuator集成
- **监控指标**: Micrometer集成
- **Web适配**: 支持Servlet、WebFlux等不同Web框架

##### 多租户支持策略

###### 域名路由模式
```
tenant1.example.com/file/xxx → Tenant1配置
tenant2.example.com/file/xxx → Tenant2配置
```

###### 路径前缀模式
```
example.com/tenant1/file/xxx → Tenant1配置
example.com/tenant2/file/xxx → Tenant2配置
```

###### 租户配置隔离
- 独立的签名密钥
- 独立的文件存储路径
- 独立的访问控制策略
- 独立的流量限制配置

##### 技术特性

- **JDK8兼容**: 全面支持JDK8语法和API
- **零依赖核心**: 签名和文件处理模块不依赖第三方库
- **插拔式设计**: 支持不同Servlet容器(Tomcat、Jetty、Undertow)
- **高性能**: 基于NIO的文件流式处理
- **内存友好**: 大文件分块处理，避免OOM
- **线程安全**: 无状态设计，支持高并发

##### 模块依赖关系
```
sxr-spring-boot-starter
    └── sxr-integration
            ├── sxr-signature-core (独立)
            ├── sxr-file-handler (独立)
            └── sxr-plugin-api (扩展接口)
```

##### 扩展能力
- **自定义签名算法**: 实现SignatureAlgorithm接口
- **自定义文件处理器**: 实现FileHandler接口
- **自定义插件**: 实现RequestPlugin接口
- **自定义存储**: 实现FileStorage接口
- **多框架适配**: 支持Spring MVC、Spring WebFlux等







#### 安装教程

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
