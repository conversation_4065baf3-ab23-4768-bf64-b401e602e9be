<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <option name="BUILD_PROCESS_HEAP_SIZE" value="4096" />
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="sxr-signature-core" />
        <module name="sxr-integration" />
        <module name="sxr-spring-boot-starter" />
        <module name="sxr-file-handler" />
      </profile>
    </annotationProcessing>
  </component>
</project>