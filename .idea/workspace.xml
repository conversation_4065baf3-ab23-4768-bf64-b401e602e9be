<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="67e0124c-3686-49aa-b391-86c5eeaff04f" name="更改" comment="add init 第一版。" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="alwaysUpdateSnapshots" value="true" />
        <option name="customMavenHome" value="$USER_HOME$/tool/apache-maven-3.9.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="$USER_HOME$/tool/apache-maven-3.9.3/conf/settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="2yATcNp8AR0VwaWxnSMEGsflBhP" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/IdeaProjects/sxrFileSignature",
    "settings.editor.selected.configurable": "File.Encoding"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="sxrFileSignature" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="sxrFileSignature" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="67e0124c-3686-49aa-b391-86c5eeaff04f" name="更改" comment="" />
      <created>1749276457501</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749276457501</updated>
    </task>
    <task id="LOCAL-00001" summary="add init 第一版。">
      <option name="closed" value="true" />
      <created>1749276581546</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749276581546</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="add init 第一版。" />
    <option name="LAST_COMMIT_MESSAGE" value="add init 第一版。" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
</project>