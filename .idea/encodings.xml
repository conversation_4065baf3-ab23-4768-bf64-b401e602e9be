<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-file-handler/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-file-handler/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-integration/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-integration/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-signature-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-signature-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-spring-boot-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sxr-spring-boot-starter/src/main/resources" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>