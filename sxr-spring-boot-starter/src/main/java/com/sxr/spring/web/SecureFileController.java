package com.sxr.spring.web;

import com.sxr.file.FileResponse;
import com.sxr.integration.SecureFileRequest;
import com.sxr.integration.SecureFileService;
import com.sxr.spring.SxrFileSignatureProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 安全文件控制器
 * 提供文件下载和预览的HTTP接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/sxr")
public class SecureFileController {
    
    private final SecureFileService secureFileService;
    private final SxrFileSignatureProperties properties;
    
    public SecureFileController(SecureFileService secureFileService, SxrFileSignatureProperties properties) {
        this.secureFileService = secureFileService;
        this.properties = properties;
    }
    
    /**
     * 安全文件下载
     */
    @GetMapping("/download/**")
    public void download(HttpServletRequest request, HttpServletResponse response) throws IOException {
        SecureFileRequest fileRequest = buildSecureFileRequest(request, false);
        FileResponse fileResponse = secureFileService.handleSecureDownload(fileRequest);
        writeResponse(fileResponse, response);
    }
    
    /**
     * 安全文件预览
     */
    @GetMapping("/preview/**")
    public void preview(HttpServletRequest request, HttpServletResponse response) throws IOException {
        SecureFileRequest fileRequest = buildSecureFileRequest(request, true);
        FileResponse fileResponse = secureFileService.handleSecurePreview(fileRequest);
        writeResponse(fileResponse, response);
    }
    
    /**
     * 生成安全URL
     */
    @PostMapping("/generate-url")
    public ResponseEntity<Map<String, String>> generateUrl(@RequestBody SecureFileRequest request) {
        try {
            String secureUrl = secureFileService.generateSecureUrl(request);
            Map<String, String> result = new HashMap<>();
            result.put("url", secureUrl);
            result.put("success", "true");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("success", "false");
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> status = new HashMap<>();
        status.put("status", "UP");
        status.put("service", "SXR File Signature");
        status.put("version", "1.0.0");
        return ResponseEntity.ok(status);
    }
    
    /**
     * 构建安全文件请求
     */
    private SecureFileRequest buildSecureFileRequest(HttpServletRequest request, boolean isPreview) {
        SecureFileRequest fileRequest = new SecureFileRequest();
        
        // 解析路径
        String requestURI = request.getRequestURI();
        String pathInfo = extractPathInfo(requestURI, isPreview);
        
        // 解析租户ID和文件路径
        parseTenantAndFilePath(pathInfo, fileRequest);
        
        // 设置基本参数
        fileRequest.setPreview(isPreview);
        fileRequest.setSignature(request.getParameter("signature"));
        fileRequest.setClientIp(getClientIp(request));
        fileRequest.setUserAgent(request.getHeader("User-Agent"));
        fileRequest.setRangeHeader(request.getHeader("Range"));
        
        // 设置过期时间
        String expireParam = request.getParameter("expire");
        if (StringUtils.hasText(expireParam)) {
            try {
                fileRequest.setExpireTime(Long.parseLong(expireParam));
            } catch (NumberFormatException e) {
                // 忽略无效的过期时间参数
            }
        }
        
        // 设置访问限制
        String limitParam = request.getParameter("limit");
        if (StringUtils.hasText(limitParam)) {
            try {
                fileRequest.setAccessLimit(Integer.parseInt(limitParam));
            } catch (NumberFormatException e) {
                // 忽略无效的限制参数
            }
        }
        
        // 设置请求头
        Map<String, String> headers = new HashMap<>();
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        fileRequest.setHeaders(headers);
        
        return fileRequest;
    }
    
    /**
     * 提取路径信息
     */
    private String extractPathInfo(String requestURI, boolean isPreview) {
        String prefix = isPreview ? "/sxr/preview/" : "/sxr/download/";
        if (requestURI.startsWith(prefix)) {
            return requestURI.substring(prefix.length());
        }
        return requestURI;
    }
    
    /**
     * 解析租户ID和文件路径
     */
    private void parseTenantAndFilePath(String pathInfo, SecureFileRequest fileRequest) {
        if (!StringUtils.hasText(pathInfo)) {
            return;
        }
        
        // 如果启用了多租户模式
        if (properties.getMultiTenant().isEnabled()) {
            String[] parts = pathInfo.split("/", 2);
            if (parts.length >= 2) {
                fileRequest.setTenantId(parts[0]);
                fileRequest.setFilePath(parts[1]);
            } else {
                fileRequest.setFilePath(pathInfo);
            }
        } else {
            fileRequest.setFilePath(pathInfo);
        }
        
        // 设置文件名
        String filePath = fileRequest.getFilePath();
        if (StringUtils.hasText(filePath)) {
            int lastSlashIndex = filePath.lastIndexOf('/');
            if (lastSlashIndex >= 0 && lastSlashIndex < filePath.length() - 1) {
                fileRequest.setFileName(filePath.substring(lastSlashIndex + 1));
            } else {
                fileRequest.setFileName(filePath);
            }
        }
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 写入响应
     */
    private void writeResponse(FileResponse fileResponse, HttpServletResponse response) throws IOException {
        // 设置状态码
        response.setStatus(fileResponse.getStatusCode());
        
        if (!fileResponse.isSuccess()) {
            // 错误响应
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"" + fileResponse.getErrorMessage() + "\"}");
            return;
        }
        
        // 设置响应头
        for (Map.Entry<String, String> header : fileResponse.getHeaders().entrySet()) {
            response.setHeader(header.getKey(), header.getValue());
        }
        
        // 设置内容长度
        if (fileResponse.getContentLength() > 0) {
            response.setContentLengthLong(fileResponse.getContentLength());
        }
        
        // 写入文件内容
        if (fileResponse.getInputStream() != null) {
            try (InputStream inputStream = fileResponse.getInputStream();
                 OutputStream outputStream = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        }
    }
}
