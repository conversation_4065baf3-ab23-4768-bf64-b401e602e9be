package com.sxr.spring;

import com.sxr.file.FileHandler;
import com.sxr.file.impl.DefaultFileHandler;
import com.sxr.integration.SecureFileService;
import com.sxr.integration.impl.DefaultSecureFileService;
import com.sxr.signature.SignatureService;
import com.sxr.signature.impl.HmacSignatureService;
import com.sxr.spring.web.SecureFileController;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SXR文件签名自动配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableConfigurationProperties(SxrFileSignatureProperties.class)
@ConditionalOnProperty(prefix = "sxr.file-signature", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SxrFileSignatureAutoConfiguration {
    
    /**
     * 签名服务Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public SignatureService signatureService() {
        return new HmacSignatureService();
    }
    
    /**
     * 文件处理器Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public FileHandler fileHandler() {
        return new DefaultFileHandler();
    }
    
    /**
     * 安全文件服务Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public SecureFileService secureFileService(SignatureService signatureService, FileHandler fileHandler) {
        return new DefaultSecureFileService(signatureService, fileHandler);
    }
    
    /**
     * 安全文件控制器Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public SecureFileController secureFileController(SecureFileService secureFileService, 
                                                   SxrFileSignatureProperties properties) {
        return new SecureFileController(secureFileService, properties);
    }
}
