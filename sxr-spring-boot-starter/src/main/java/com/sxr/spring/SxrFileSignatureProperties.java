package com.sxr.spring;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * SXR文件签名配置属性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ConfigurationProperties(prefix = "sxr.file-signature")
public class SxrFileSignatureProperties {
    
    /** 是否启用文件签名功能 */
    private boolean enabled = true;
    
    /** 默认密钥 */
    private String defaultSecretKey = "sxr-default-secret-key";
    
    /** 文件存储基础路径 */
    private String basePath = "./files";
    
    /** 默认过期时间（秒） */
    private Long defaultExpireSeconds = 3600L;
    
    /** 最大文件大小（字节） */
    private Long maxFileSize = 100 * 1024 * 1024L;
    
    /** 是否启用访问日志 */
    private boolean enableAccessLog = true;
    
    /** 多租户配置 */
    private MultiTenant multiTenant = new MultiTenant();
    
    /** 租户配置映射 */
    private Map<String, TenantProperties> tenants = new HashMap<>();
    
    // Getter and Setter methods
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getDefaultSecretKey() {
        return defaultSecretKey;
    }
    
    public void setDefaultSecretKey(String defaultSecretKey) {
        this.defaultSecretKey = defaultSecretKey;
    }
    
    public String getBasePath() {
        return basePath;
    }
    
    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }
    
    public Long getDefaultExpireSeconds() {
        return defaultExpireSeconds;
    }
    
    public void setDefaultExpireSeconds(Long defaultExpireSeconds) {
        this.defaultExpireSeconds = defaultExpireSeconds;
    }
    
    public Long getMaxFileSize() {
        return maxFileSize;
    }
    
    public void setMaxFileSize(Long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }
    
    public boolean isEnableAccessLog() {
        return enableAccessLog;
    }
    
    public void setEnableAccessLog(boolean enableAccessLog) {
        this.enableAccessLog = enableAccessLog;
    }
    
    public MultiTenant getMultiTenant() {
        return multiTenant;
    }
    
    public void setMultiTenant(MultiTenant multiTenant) {
        this.multiTenant = multiTenant;
    }
    
    public Map<String, TenantProperties> getTenants() {
        return tenants;
    }
    
    public void setTenants(Map<String, TenantProperties> tenants) {
        this.tenants = tenants;
    }
    
    /**
     * 多租户配置
     */
    public static class MultiTenant {
        /** 是否启用多租户 */
        private boolean enabled = false;
        
        /** 租户识别模式：domain（域名）或 prefix（路径前缀） */
        private String mode = "prefix";
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getMode() {
            return mode;
        }
        
        public void setMode(String mode) {
            this.mode = mode;
        }
    }
    
    /**
     * 租户属性配置
     */
    public static class TenantProperties {
        /** 租户名称 */
        private String name;
        
        /** 租户密钥 */
        private String secretKey;
        
        /** 文件存储路径 */
        private String basePath;
        
        /** 过期时间（秒） */
        private Long expireSeconds;
        
        /** 最大文件大小 */
        private Long maxFileSize;
        
        /** 允许的文件扩展名 */
        private String[] allowedExtensions;
        
        /** 禁止的文件扩展名 */
        private String[] forbiddenExtensions;
        
        /** 是否启用限流 */
        private boolean enableRateLimit = false;
        
        /** 每秒最大请求数 */
        private Integer maxRequestsPerSecond = 100;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getSecretKey() {
            return secretKey;
        }
        
        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }
        
        public String getBasePath() {
            return basePath;
        }
        
        public void setBasePath(String basePath) {
            this.basePath = basePath;
        }
        
        public Long getExpireSeconds() {
            return expireSeconds;
        }
        
        public void setExpireSeconds(Long expireSeconds) {
            this.expireSeconds = expireSeconds;
        }
        
        public Long getMaxFileSize() {
            return maxFileSize;
        }
        
        public void setMaxFileSize(Long maxFileSize) {
            this.maxFileSize = maxFileSize;
        }
        
        public String[] getAllowedExtensions() {
            return allowedExtensions;
        }
        
        public void setAllowedExtensions(String[] allowedExtensions) {
            this.allowedExtensions = allowedExtensions;
        }
        
        public String[] getForbiddenExtensions() {
            return forbiddenExtensions;
        }
        
        public void setForbiddenExtensions(String[] forbiddenExtensions) {
            this.forbiddenExtensions = forbiddenExtensions;
        }
        
        public boolean isEnableRateLimit() {
            return enableRateLimit;
        }
        
        public void setEnableRateLimit(boolean enableRateLimit) {
            this.enableRateLimit = enableRateLimit;
        }
        
        public Integer getMaxRequestsPerSecond() {
            return maxRequestsPerSecond;
        }
        
        public void setMaxRequestsPerSecond(Integer maxRequestsPerSecond) {
            this.maxRequestsPerSecond = maxRequestsPerSecond;
        }
    }
}
