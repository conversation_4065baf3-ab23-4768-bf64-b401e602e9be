{"groups": [{"name": "sxr.file-signature", "type": "com.sxr.spring.SxrFileSignatureProperties", "sourceType": "com.sxr.spring.SxrFileSignatureProperties"}, {"name": "sxr.file-signature.multi-tenant", "type": "com.sxr.spring.SxrFileSignatureProperties$MultiTenant", "sourceType": "com.sxr.spring.SxrFileSignatureProperties", "sourceMethod": "getMultiTenant()"}], "properties": [{"name": "sxr.file-signature.base-path", "type": "java.lang.String", "description": "文件存储基础路径", "sourceType": "com.sxr.spring.SxrFileSignatureProperties", "defaultValue": "./files"}, {"name": "sxr.file-signature.default-expire-seconds", "type": "java.lang.Long", "description": "默认过期时间（秒）", "sourceType": "com.sxr.spring.SxrFileSignatureProperties", "defaultValue": 3600}, {"name": "sxr.file-signature.default-secret-key", "type": "java.lang.String", "description": "默认密钥", "sourceType": "com.sxr.spring.SxrFileSignatureProperties", "defaultValue": "sxr-default-secret-key"}, {"name": "sxr.file-signature.enable-access-log", "type": "java.lang.Bo<PERSON>an", "description": "是否启用访问日志", "sourceType": "com.sxr.spring.SxrFileSignatureProperties", "defaultValue": true}, {"name": "sxr.file-signature.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用文件签名功能", "sourceType": "com.sxr.spring.SxrFileSignatureProperties", "defaultValue": true}, {"name": "sxr.file-signature.max-file-size", "type": "java.lang.Long", "description": "最大文件大小（字节）", "sourceType": "com.sxr.spring.SxrFileSignatureProperties"}, {"name": "sxr.file-signature.multi-tenant.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用多租户", "sourceType": "com.sxr.spring.SxrFileSignatureProperties$MultiTenant", "defaultValue": false}, {"name": "sxr.file-signature.multi-tenant.mode", "type": "java.lang.String", "description": "租户识别模式：domain（域名）或 prefix（路径前缀）", "sourceType": "com.sxr.spring.SxrFileSignatureProperties$MultiTenant", "defaultValue": "prefix"}, {"name": "sxr.file-signature.tenants", "type": "java.util.Map<java.lang.String,com.sxr.spring.SxrFileSignatureProperties$TenantProperties>", "description": "租户配置映射", "sourceType": "com.sxr.spring.SxrFileSignatureProperties"}], "hints": []}