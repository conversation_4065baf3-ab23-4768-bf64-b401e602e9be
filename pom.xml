<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sxr</groupId>
    <artifactId>sxr-file-signature</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>SXR File Signature</name>
    <description>文件签名保护系统 - 多层架构设计</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        
        <!-- Spring Boot 3.x 需要 JDK17+，这里使用兼容JDK8的版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
        <junit.version>4.13.2</junit.version>
        <slf4j.version>1.7.36</slf4j.version>
    </properties>

    <modules>
        <module>sxr-signature-core</module>
        <module>sxr-file-handler</module>
        <module>sxr-integration</module>
        <module>sxr-spring-boot-starter</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.sxr</groupId>
                <artifactId>sxr-signature-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sxr</groupId>
                <artifactId>sxr-file-handler</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sxr</groupId>
                <artifactId>sxr-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <!-- 测试依赖 -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>8</source>
                        <target>8</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
