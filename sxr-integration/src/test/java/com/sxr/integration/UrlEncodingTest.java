package com.sxr.integration;

import com.sxr.file.FileHandler;
import com.sxr.file.impl.DefaultFileHandler;
import com.sxr.integration.impl.DefaultSecureFileService;
import com.sxr.signature.SignatureService;
import com.sxr.signature.impl.HmacSignatureService;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * URL编码测试
 * 验证签名URL的编码和解码功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UrlEncodingTest {
    
    private DefaultSecureFileService secureFileService;
    
    @Before
    public void setUp() {
        SignatureService signatureService = new HmacSignatureService();
        FileHandler fileHandler = new DefaultFileHandler();
        secureFileService = new DefaultSecureFileService(signatureService, fileHandler);
    }
    
    @Test
    public void testUrlEncodingInGeneratedUrl() {
        // 准备测试数据
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant1");
        request.setFilePath("images/test.jpg");
        request.setFileName("test.jpg");
        request.setExpireTime(System.currentTimeMillis() + 3600000);
        request.setPreview(true);
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证URL编码
        assertNotNull("安全URL不能为空", secureUrl);
        System.out.println("生成的URL: " + secureUrl);
        
        // 检查是否包含编码后的字符
        if (secureUrl.contains("+")) {
            fail("URL中不应该包含未编码的+号");
        }
        
        // 应该包含编码后的字符
        assertTrue("URL应该包含%2B（编码后的+号）", 
                  secureUrl.contains("%2B") || !secureUrl.contains("+"));
        assertTrue("URL应该包含%3D（编码后的=号）", 
                  secureUrl.contains("%3D") || !secureUrl.contains("="));
    }
    
    @Test
    public void testSignatureWithSpecialCharacters() {
        // 创建一个包含特殊字符的测试场景
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("test+tenant"); // 包含+号的租户ID
        request.setFilePath("path/with+plus/file.jpg");
        request.setFileName("file+name.jpg");
        request.setExpireTime(System.currentTimeMillis() + 3600000);
        request.setPreview(false);
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        System.out.println("包含特殊字符的URL: " + secureUrl);
        
        // 验证路径中的+号被正确处理
        assertTrue("URL应该包含正确的路径", secureUrl.contains("test+tenant"));
        assertTrue("URL应该包含签名参数", secureUrl.contains("signature="));
    }
    
    @Test
    public void testUrlDecoding() {
        // 模拟URL解码过程
        String originalSignature = "C+fIK6PRw95JZRO6UlRHSvfpk/v1VJIbjvByxvI2WxA=";
        String encodedSignature = "C%2BfIK6PRw95JZRO6UlRHSvfpk%2Fv1VJIbjvByxvI2WxA%3D";
        
        // 手动解码测试
        String decoded = urlDecode(encodedSignature);
        assertEquals("解码后应该恢复原始签名", originalSignature, decoded);
        
        System.out.println("原始签名: " + originalSignature);
        System.out.println("编码签名: " + encodedSignature);
        System.out.println("解码签名: " + decoded);
    }
    
    /**
     * 简单的URL解码实现（用于测试）
     */
    private String urlDecode(String value) {
        if (value == null) {
            return null;
        }
        
        StringBuilder decoded = new StringBuilder();
        for (int i = 0; i < value.length(); i++) {
            char c = value.charAt(i);
            if (c == '%' && i + 2 < value.length()) {
                try {
                    String hex = value.substring(i + 1, i + 3);
                    int charCode = Integer.parseInt(hex, 16);
                    decoded.append((char) charCode);
                    i += 2;
                } catch (NumberFormatException e) {
                    decoded.append(c);
                }
            } else {
                decoded.append(c);
            }
        }
        return decoded.toString();
    }
}
