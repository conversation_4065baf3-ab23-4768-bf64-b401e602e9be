package com.sxr.integration;

import com.sxr.file.FileHandler;
import com.sxr.file.impl.DefaultFileHandler;
import com.sxr.integration.impl.DefaultSecureFileService;
import com.sxr.signature.SignatureService;
import com.sxr.signature.impl.HmacSignatureService;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * extraParams功能测试
 * 验证扩展参数在签名生成和验证中的正确使用
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ExtraParamsTest {
    
    private DefaultSecureFileService secureFileService;
    
    @Before
    public void setUp() {
        SignatureService signatureService = new HmacSignatureService();
        FileHandler fileHandler = new DefaultFileHandler();
        secureFileService = new DefaultSecureFileService(signatureService, fileHandler);
    }
    
    @Test
    public void testGenerateUrlWithExtraParams() {
        // 准备测试数据
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant1");
        request.setFilePath("images/test.jpg");
        request.setFileName("test.jpg");
        request.setExpireTime(System.currentTimeMillis() + 3600000);
        request.setPreview(true);
        
        // 添加extraParams
        Map<String, String> extraParams = new HashMap<>();
        extraParams.put("userId", "12345");
        extraParams.put("source", "mobile");
        extraParams.put("version", "1.0");
        request.setExtraParams(extraParams);
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        System.out.println("包含extraParams的URL: " + secureUrl);
        
        // 验证URL包含extraParams
        assertTrue("URL应该包含userId参数", secureUrl.contains("userId="));
        assertTrue("URL应该包含source参数", secureUrl.contains("source="));
        assertTrue("URL应该包含version参数", secureUrl.contains("version="));
        
        // 验证URL编码
        assertTrue("URL应该包含编码后的参数值", 
                  secureUrl.contains("userId=12345") || secureUrl.contains("userId%3D12345"));
    }
    
    @Test
    public void testGenerateUrlWithSpecialCharactersInExtraParams() {
        // 准备包含特殊字符的测试数据
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant1");
        request.setFilePath("docs/file.pdf");
        request.setFileName("file.pdf");
        request.setExpireTime(System.currentTimeMillis() + 3600000);
        request.setPreview(false);
        
        // 添加包含特殊字符的extraParams
        Map<String, String> extraParams = new HashMap<>();
        extraParams.put("user+name", "john+doe");
        extraParams.put("email", "<EMAIL>");
        extraParams.put("path", "/home/<USER>/docs");
        request.setExtraParams(extraParams);
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        System.out.println("包含特殊字符extraParams的URL: " + secureUrl);
        
        // 验证特殊字符被正确编码
        assertTrue("URL应该包含编码后的+号", secureUrl.contains("%2B"));
        assertTrue("URL应该包含@符号或其编码", 
                  secureUrl.contains("@") || secureUrl.contains("%40"));
    }
    
    @Test
    public void testGenerateUrlWithoutExtraParams() {
        // 准备不包含extraParams的测试数据
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant2");
        request.setFilePath("videos/demo.mp4");
        request.setFileName("demo.mp4");
        request.setExpireTime(System.currentTimeMillis() + 7200000);
        request.setPreview(true);
        // 不设置extraParams
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        System.out.println("不包含extraParams的URL: " + secureUrl);
        
        // 验证基本参数存在
        assertTrue("URL应该包含signature参数", secureUrl.contains("signature="));
        assertTrue("URL应该包含expire参数", secureUrl.contains("expire="));
    }
    
    @Test
    public void testEmptyExtraParams() {
        // 准备包含空extraParams的测试数据
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant3");
        request.setFilePath("audio/music.mp3");
        request.setFileName("music.mp3");
        request.setExpireTime(System.currentTimeMillis() + 1800000);
        request.setPreview(true);
        
        // 设置空的extraParams
        request.setExtraParams(new HashMap<>());
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        System.out.println("空extraParams的URL: " + secureUrl);
        
        // 验证基本功能正常
        assertTrue("URL应该包含正确的路径", secureUrl.contains("tenant3/audio/music.mp3"));
        assertTrue("URL应该包含signature参数", secureUrl.contains("signature="));
    }
}
