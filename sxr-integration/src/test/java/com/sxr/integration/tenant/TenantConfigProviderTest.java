package com.sxr.integration.tenant;

import com.sxr.integration.TenantContext;
import com.sxr.integration.tenant.impl.FileTenantConfigProvider;
import com.sxr.integration.tenant.impl.MemoryTenantConfigProvider;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

/**
 * 租户配置提供者测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class TenantConfigProviderTest {

    private MemoryTenantConfigProvider memoryProvider;
    private FileTenantConfigProvider fileProvider;

    @Before
    public void setUp() {
        memoryProvider = new MemoryTenantConfigProvider();
        fileProvider = new FileTenantConfigProvider("tenant-config.json");
    }

    @Test
    public void testMemoryTenantConfigProvider() {
        // 测试默认租户
        TenantContext tenant1 = memoryProvider.getTenantContext("tenant1");
        assertNotNull("tenant1应该存在", tenant1);
        assertNotNull("tenant1应该有secretKey", tenant1.getSecretKey());

        TenantContext tenant2 = memoryProvider.getTenantContext("tenant2");
        assertNotNull("tenant2应该存在", tenant2);
        assertNotNull("tenant2应该有secretKey", tenant2.getSecretKey());

        // 测试不存在的租户
        TenantContext nonExistent = memoryProvider.getTenantContext("nonexistent");
        assertNull("不存在的租户应该返回null", nonExistent);

        // 测试获取所有租户ID
        List<String> tenantIds = memoryProvider.getAllTenantIds();
        assertTrue("应该包含tenant1", tenantIds.contains("tenant1"));
        assertTrue("应该包含tenant2", tenantIds.contains("tenant2"));

        System.out.println("内存配置提供者类型: " + memoryProvider.getProviderType());
        System.out.println("最后更新时间: " + memoryProvider.getLastUpdateTime());
    }

    @Test
    public void testMemoryTenantConfigProviderOperations() {
        // 测试添加新租户
        TenantContext newTenant = new TenantContext();
        newTenant.setSecretKey("new-tenant-key");
        newTenant.setBasePath("./files/new-tenant");

        memoryProvider.putTenantContext("newTenant", newTenant);

        TenantContext retrieved = memoryProvider.getTenantContext("newTenant");
        assertNotNull("新添加的租户应该存在", retrieved);
        assertEquals("密钥应该匹配", "new-tenant-key", retrieved.getSecretKey());

        // 测试删除租户
        TenantContext removed = memoryProvider.removeTenantContext("newTenant");
        assertNotNull("删除的租户应该返回", removed);
        assertEquals("删除的租户密钥应该匹配", "new-tenant-key", removed.getSecretKey());

        TenantContext afterRemoval = memoryProvider.getTenantContext("newTenant");
        assertNull("删除后的租户应该不存在", afterRemoval);
    }

    @Test
    public void testFileTenantConfigProvider() {
        // 测试文件配置提供者
        System.out.println("文件配置提供者类型: " + fileProvider.getProviderType());

        // 测试刷新功能
        boolean refreshResult = fileProvider.refresh();
        assertTrue("刷新应该成功", refreshResult);

        // 测试获取租户（如果配置文件存在）
        List<String> tenantIds = fileProvider.getAllTenantIds();
        System.out.println("从文件加载的租户: " + tenantIds);

        // 如果文件不存在，应该有默认配置
        if (!tenantIds.isEmpty()) {
            String firstTenantId = tenantIds.get(0);
            TenantContext context = fileProvider.getTenantContext(firstTenantId);
            assertNotNull("租户上下文应该存在", context);
            assertNotNull("密钥应该存在", context.getSecretKey());
        }
    }

    @Test
    public void testTenantConfigChangeListener() {
        // 测试配置变更监听器
        TestChangeListener listener = new TestChangeListener();
        memoryProvider.addChangeListener(listener);

        // 添加新租户，应该触发添加事件
        TenantContext testTenant = new TenantContext();
        testTenant.setSecretKey("test-key");
        memoryProvider.putTenantContext("testTenant", testTenant);

        assertTrue("应该触发租户添加事件", listener.addedCalled);
        assertEquals("添加的租户ID应该匹配", "testTenant", listener.lastTenantId);

        // 更新租户，应该触发更新事件
        TenantContext updatedTenant = new TenantContext();
        updatedTenant.setSecretKey("updated-key");
        updatedTenant.setBasePath("./files/updated");
        memoryProvider.putTenantContext("testTenant", updatedTenant);

        assertTrue("应该触发租户更新事件", listener.updatedCalled);

        // 删除租户，应该触发删除事件
        memoryProvider.removeTenantContext("testTenant");

        assertTrue("应该触发租户删除事件", listener.removedCalled);

        // 刷新配置，应该触发刷新事件
        memoryProvider.refresh();

        assertTrue("应该触发配置刷新事件", listener.refreshedCalled);
    }

    /**
     * 测试用的配置变更监听器
     */
    private static class TestChangeListener implements TenantConfigChangeListener {
        boolean addedCalled = false;
        boolean updatedCalled = false;
        boolean removedCalled = false;
        boolean refreshedCalled = false;
        String lastTenantId = null;

        @Override
        public void onTenantAdded(String tenantId, TenantContext context) {
            addedCalled = true;
            lastTenantId = tenantId;
            System.out.println("租户添加: " + tenantId);
        }

        @Override
        public void onTenantUpdated(String tenantId, TenantContext oldContext, TenantContext newContext) {
            updatedCalled = true;
            lastTenantId = tenantId;
            System.out.println("租户更新: " + tenantId);
        }

        @Override
        public void onTenantRemoved(String tenantId, TenantContext context) {
            removedCalled = true;
            lastTenantId = tenantId;
            System.out.println("租户删除: " + tenantId);
        }

        @Override
        public void onConfigRefreshed(TenantConfigProvider provider) {
            refreshedCalled = true;
            System.out.println("配置刷新: " + provider.getProviderType());
        }
    }
}
