package com.sxr.integration.impl;

import com.sxr.file.FileHandler;
import com.sxr.file.impl.DefaultFileHandler;
import com.sxr.integration.SecureFileRequest;
import com.sxr.integration.SecureFileService;
import com.sxr.signature.SignatureService;
import com.sxr.signature.impl.HmacSignatureService;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 默认安全文件服务测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DefaultSecureFileServiceTest {
    
    private SecureFileService secureFileService;
    
    @Before
    public void setUp() {
        SignatureService signatureService = new HmacSignatureService();
        FileHandler fileHandler = new DefaultFileHandler();
        secureFileService = new DefaultSecureFileService(signatureService, fileHandler);
    }
    
    @Test
    public void testGenerateSecureUrl() {
        // 准备测试数据
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant1");
        request.setFilePath("images/test.jpg");
        request.setFileName("test.jpg");
        request.setExpireTime(System.currentTimeMillis() + 3600000); // 1小时后过期
        request.setPreview(true);
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        assertTrue("URL应该包含正确的路径", secureUrl.contains("/sxr/preview/tenant1/images/test.jpg"));
        assertTrue("URL应该包含签名参数", secureUrl.contains("signature="));
        assertTrue("URL应该包含过期时间参数", secureUrl.contains("expire="));
        
        System.out.println("生成的安全URL: " + secureUrl);
    }
    
    @Test
    public void testGenerateSecureUrlForDownload() {
        // 准备测试数据
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant2");
        request.setFilePath("documents/report.pdf");
        request.setFileName("report.pdf");
        request.setExpireTime(System.currentTimeMillis() + 7200000); // 2小时后过期
        request.setPreview(false); // 下载模式
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        assertTrue("URL应该包含下载路径", secureUrl.contains("/sxr/download/tenant2/documents/report.pdf"));
        assertTrue("URL应该包含签名参数", secureUrl.contains("signature="));
        assertTrue("URL应该包含过期时间参数", secureUrl.contains("expire="));
        
        System.out.println("生成的下载URL: " + secureUrl);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateSecureUrlWithNullRequest() {
        secureFileService.generateSecureUrl(null);
    }
    
    @Test
    public void testGenerateSecureUrlWithoutExpireTime() {
        // 准备测试数据（不设置过期时间）
        SecureFileRequest request = new SecureFileRequest();
        request.setTenantId("tenant3");
        request.setFilePath("videos/demo.mp4");
        request.setFileName("demo.mp4");
        request.setPreview(true);
        
        // 生成安全URL
        String secureUrl = secureFileService.generateSecureUrl(request);
        
        // 验证结果
        assertNotNull("安全URL不能为空", secureUrl);
        assertTrue("URL应该包含正确的路径", secureUrl.contains("/sxr/preview/tenant3/videos/demo.mp4"));
        assertTrue("URL应该包含签名参数", secureUrl.contains("signature="));
        assertFalse("URL不应该包含过期时间参数", secureUrl.contains("expire="));
        
        System.out.println("生成的无过期时间URL: " + secureUrl);
    }
}
