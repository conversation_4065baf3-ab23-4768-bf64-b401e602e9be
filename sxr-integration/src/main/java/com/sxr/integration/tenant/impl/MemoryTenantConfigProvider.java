package com.sxr.integration.tenant.impl;

import com.sxr.integration.TenantContext;
import com.sxr.integration.tenant.TenantConfigChangeListener;
import com.sxr.integration.tenant.TenantConfigProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 基于内存的租户配置提供者
 * 适用于测试环境或简单的静态配置场景
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class MemoryTenantConfigProvider implements TenantConfigProvider {
    
    private final Map<String, TenantContext> tenantContexts = new ConcurrentHashMap<>();
    private final List<TenantConfigChangeListener> listeners = new CopyOnWriteArrayList<>();
    private volatile long lastUpdateTime;
    
    public MemoryTenantConfigProvider() {
        loadDefaultConfig();
    }
    
    @Override
    public TenantContext getTenantContext(String tenantId) {
        return tenantContexts.get(tenantId);
    }
    
    @Override
    public List<String> getAllTenantIds() {
        return new ArrayList<>(tenantContexts.keySet());
    }
    
    @Override
    public boolean refresh() {
        // 内存配置无需刷新，但触发刷新事件
        notifyConfigRefreshed();
        return true;
    }
    
    @Override
    public String getProviderType() {
        return "Memory-based";
    }
    
    @Override
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    /**
     * 添加或更新租户配置
     * 
     * @param tenantId 租户ID
     * @param context 租户上下文
     */
    public void putTenantContext(String tenantId, TenantContext context) {
        TenantContext oldContext = tenantContexts.get(tenantId);
        tenantContexts.put(tenantId, context);
        lastUpdateTime = System.currentTimeMillis();
        
        if (oldContext == null) {
            notifyTenantAdded(tenantId, context);
        } else if (!oldContext.equals(context)) {
            notifyTenantUpdated(tenantId, oldContext, context);
        }
    }
    
    /**
     * 删除租户配置
     * 
     * @param tenantId 租户ID
     * @return 被删除的租户上下文
     */
    public TenantContext removeTenantContext(String tenantId) {
        TenantContext removedContext = tenantContexts.remove(tenantId);
        if (removedContext != null) {
            lastUpdateTime = System.currentTimeMillis();
            notifyTenantRemoved(tenantId, removedContext);
        }
        return removedContext;
    }
    
    /**
     * 清空所有租户配置
     */
    public void clear() {
        Map<String, TenantContext> oldContexts = new ConcurrentHashMap<>(tenantContexts);
        tenantContexts.clear();
        lastUpdateTime = System.currentTimeMillis();
        
        // 通知所有租户被删除
        for (Map.Entry<String, TenantContext> entry : oldContexts.entrySet()) {
            notifyTenantRemoved(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 添加配置变更监听器
     */
    public void addChangeListener(TenantConfigChangeListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 移除配置变更监听器
     */
    public void removeChangeListener(TenantConfigChangeListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfig() {
        // 默认租户1
        TenantContext tenant1Context = new TenantContext();
        tenant1Context.setSecretKey("default-secret-key-change-in-production");
        tenant1Context.setBasePath("./files/tenant1");
        tenantContexts.put("tenant1", tenant1Context);
        
        // 默认租户2
        TenantContext tenant2Context = new TenantContext();
        tenant2Context.setSecretKey("tenant2-secret-key");
        tenant2Context.setBasePath("./files/tenant2");
        tenantContexts.put("tenant2", tenant2Context);
        
        lastUpdateTime = System.currentTimeMillis();
    }
    
    // 事件通知方法
    private void notifyTenantAdded(String tenantId, TenantContext context) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantAdded(tenantId, context);
            } catch (Exception e) {
                System.err.println("Error notifying tenant added: " + e.getMessage());
            }
        }
    }
    
    private void notifyTenantUpdated(String tenantId, TenantContext oldContext, TenantContext newContext) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantUpdated(tenantId, oldContext, newContext);
            } catch (Exception e) {
                System.err.println("Error notifying tenant updated: " + e.getMessage());
            }
        }
    }
    
    private void notifyTenantRemoved(String tenantId, TenantContext context) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantRemoved(tenantId, context);
            } catch (Exception e) {
                System.err.println("Error notifying tenant removed: " + e.getMessage());
            }
        }
    }
    
    private void notifyConfigRefreshed() {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onConfigRefreshed(this);
            } catch (Exception e) {
                System.err.println("Error notifying config refreshed: " + e.getMessage());
            }
        }
    }
}
