package com.sxr.integration.tenant;

import com.sxr.integration.TenantContext;

/**
 * 租户配置变更监听器
 * 用于监听配置变更事件，实现热更新
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TenantConfigChangeListener {
    
    /**
     * 租户配置添加事件
     * 
     * @param tenantId 租户ID
     * @param context 租户上下文
     */
    void onTenantAdded(String tenantId, TenantContext context);
    
    /**
     * 租户配置更新事件
     * 
     * @param tenantId 租户ID
     * @param oldContext 旧的租户上下文
     * @param newContext 新的租户上下文
     */
    void onTenantUpdated(String tenantId, TenantContext oldContext, TenantContext newContext);
    
    /**
     * 租户配置删除事件
     * 
     * @param tenantId 租户ID
     * @param context 被删除的租户上下文
     */
    void onTenantRemoved(String tenantId, TenantContext context);
    
    /**
     * 配置刷新事件
     * 
     * @param provider 配置提供者
     */
    void onConfigRefreshed(TenantConfigProvider provider);
}
