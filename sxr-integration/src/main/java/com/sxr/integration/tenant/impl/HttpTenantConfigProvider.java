package com.sxr.integration.tenant.impl;

import com.sxr.integration.TenantContext;
import com.sxr.integration.tenant.TenantConfigChangeListener;
import com.sxr.integration.tenant.TenantConfigProvider;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 基于HTTP接口的租户配置提供者
 * 支持从远程HTTP接口获取配置
 * 支持定时轮询和热更新
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class HttpTenantConfigProvider implements TenantConfigProvider {
    
    private final String configUrl;
    private final int refreshIntervalSeconds;
    private final Map<String, TenantContext> tenantContexts = new ConcurrentHashMap<>();
    private final List<TenantConfigChangeListener> listeners = new CopyOnWriteArrayList<>();
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    private volatile long lastUpdateTime = 0;
    private volatile boolean started = false;
    
    public HttpTenantConfigProvider(String configUrl) {
        this(configUrl, 60); // 默认60秒刷新一次
    }
    
    public HttpTenantConfigProvider(String configUrl, int refreshIntervalSeconds) {
        this.configUrl = configUrl;
        this.refreshIntervalSeconds = refreshIntervalSeconds;
        
        // 初始加载配置
        loadConfig();
        
        // 启动定时刷新
        startPeriodicRefresh();
    }
    
    @Override
    public TenantContext getTenantContext(String tenantId) {
        return tenantContexts.get(tenantId);
    }
    
    @Override
    public List<String> getAllTenantIds() {
        return new ArrayList<>(tenantContexts.keySet());
    }
    
    @Override
    public boolean refresh() {
        return loadConfig();
    }
    
    @Override
    public String getProviderType() {
        return "HTTP-based (" + configUrl + ")";
    }
    
    @Override
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    /**
     * 添加配置变更监听器
     */
    public void addChangeListener(TenantConfigChangeListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 移除配置变更监听器
     */
    public void removeChangeListener(TenantConfigChangeListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 停止定时刷新
     */
    public void shutdown() {
        started = false;
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 启动定时刷新
     */
    private void startPeriodicRefresh() {
        if (started) {
            return;
        }
        
        started = true;
        scheduler.scheduleWithFixedDelay(
            this::refresh,
            refreshIntervalSeconds,
            refreshIntervalSeconds,
            TimeUnit.SECONDS
        );
    }
    
    /**
     * 从HTTP接口加载配置
     */
    private boolean loadConfig() {
        try {
            String jsonResponse = fetchConfigFromHttp();
            if (jsonResponse != null) {
                parseAndUpdateConfig(jsonResponse);
                lastUpdateTime = System.currentTimeMillis();
                notifyConfigRefreshed();
                return true;
            }
        } catch (Exception e) {
            System.err.println("Failed to load config from HTTP: " + e.getMessage());
            
            // 如果是首次加载失败，使用默认配置
            if (tenantContexts.isEmpty()) {
                loadDefaultConfig();
            }
        }
        return false;
    }
    
    /**
     * 从HTTP接口获取配置
     */
    private String fetchConfigFromHttp() throws IOException {
        URL url = new URL(configUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒连接超时
            connection.setReadTimeout(10000);   // 10秒读取超时
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "SXR-FileSignature/1.0");
            
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                return response.toString();
            } else {
                System.err.println("HTTP request failed with code: " + responseCode);
                return null;
            }
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * 解析并更新配置
     */
    private void parseAndUpdateConfig(String jsonResponse) {
        Map<String, TenantContext> newContexts = new ConcurrentHashMap<>();
        
        // 解析JSON响应
        // 期望格式: {"tenants": {"tenant1": {"secretKey": "key1", "basePath": "path1"}, ...}}
        
        String tenantsPattern = "\"tenants\"\\s*:\\s*\\{([^}]+)\\}";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(tenantsPattern);
        java.util.regex.Matcher matcher = pattern.matcher(jsonResponse);
        
        if (matcher.find()) {
            String tenantsContent = matcher.group(1);
            
            // 解析每个租户配置
            String tenantPattern = "\"([^\"]+)\"\\s*:\\s*\\{([^}]+)\\}";
            pattern = java.util.regex.Pattern.compile(tenantPattern);
            matcher = pattern.matcher(tenantsContent);
            
            while (matcher.find()) {
                String tenantId = matcher.group(1);
                String tenantConfig = matcher.group(2);
                
                TenantContext context = parseTenantConfig(tenantConfig);
                if (context != null) {
                    newContexts.put(tenantId, context);
                }
            }
        }
        
        updateTenantContexts(newContexts);
    }
    
    /**
     * 解析单个租户配置
     */
    private TenantContext parseTenantConfig(String config) {
        TenantContext context = new TenantContext();
        
        // 解析secretKey
        String secretKeyPattern = "\"secretKey\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(secretKeyPattern);
        java.util.regex.Matcher matcher = pattern.matcher(config);
        if (matcher.find()) {
            context.setSecretKey(matcher.group(1));
        }
        
        // 解析basePath
        String basePathPattern = "\"basePath\"\\s*:\\s*\"([^\"]+)\"";
        pattern = java.util.regex.Pattern.compile(basePathPattern);
        matcher = pattern.matcher(config);
        if (matcher.find()) {
            context.setBasePath(matcher.group(1));
        }
        
        return context.getSecretKey() != null ? context : null;
    }
    
    /**
     * 更新租户上下文并触发变更事件
     */
    private void updateTenantContexts(Map<String, TenantContext> newContexts) {
        Map<String, TenantContext> oldContexts = new ConcurrentHashMap<>(tenantContexts);
        
        // 检查新增和更新的租户
        for (Map.Entry<String, TenantContext> entry : newContexts.entrySet()) {
            String tenantId = entry.getKey();
            TenantContext newContext = entry.getValue();
            TenantContext oldContext = oldContexts.get(tenantId);
            
            if (oldContext == null) {
                notifyTenantAdded(tenantId, newContext);
            } else if (!oldContext.equals(newContext)) {
                notifyTenantUpdated(tenantId, oldContext, newContext);
            }
        }
        
        // 检查删除的租户
        for (Map.Entry<String, TenantContext> entry : oldContexts.entrySet()) {
            String tenantId = entry.getKey();
            if (!newContexts.containsKey(tenantId)) {
                notifyTenantRemoved(tenantId, entry.getValue());
            }
        }
        
        // 更新内存中的配置
        tenantContexts.clear();
        tenantContexts.putAll(newContexts);
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfig() {
        Map<String, TenantContext> defaultContexts = new ConcurrentHashMap<>();
        
        TenantContext defaultContext = new TenantContext();
        defaultContext.setSecretKey("default-secret-key-change-in-production");
        defaultContext.setBasePath("./files");
        defaultContexts.put("tenant1", defaultContext);
        
        updateTenantContexts(defaultContexts);
        lastUpdateTime = System.currentTimeMillis();
    }
    
    // 事件通知方法
    private void notifyTenantAdded(String tenantId, TenantContext context) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantAdded(tenantId, context);
            } catch (Exception e) {
                System.err.println("Error notifying tenant added: " + e.getMessage());
            }
        }
    }
    
    private void notifyTenantUpdated(String tenantId, TenantContext oldContext, TenantContext newContext) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantUpdated(tenantId, oldContext, newContext);
            } catch (Exception e) {
                System.err.println("Error notifying tenant updated: " + e.getMessage());
            }
        }
    }
    
    private void notifyTenantRemoved(String tenantId, TenantContext context) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantRemoved(tenantId, context);
            } catch (Exception e) {
                System.err.println("Error notifying tenant removed: " + e.getMessage());
            }
        }
    }
    
    private void notifyConfigRefreshed() {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onConfigRefreshed(this);
            } catch (Exception e) {
                System.err.println("Error notifying config refreshed: " + e.getMessage());
            }
        }
    }
}
