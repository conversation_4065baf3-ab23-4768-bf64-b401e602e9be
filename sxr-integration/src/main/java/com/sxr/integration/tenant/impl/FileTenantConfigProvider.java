package com.sxr.integration.tenant.impl;

import com.sxr.integration.TenantContext;
import com.sxr.integration.tenant.TenantConfigChangeListener;
import com.sxr.integration.tenant.TenantConfigProvider;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 基于文件的租户配置提供者
 * 支持从properties文件或JSON文件读取配置
 * 支持文件变更监听和热更新
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class FileTenantConfigProvider implements TenantConfigProvider {
    
    private final String configFilePath;
    private final Map<String, TenantContext> tenantContexts = new ConcurrentHashMap<>();
    private final List<TenantConfigChangeListener> listeners = new CopyOnWriteArrayList<>();
    private volatile long lastUpdateTime = 0;
    private volatile long lastFileModified = 0;
    
    public FileTenantConfigProvider(String configFilePath) {
        this.configFilePath = configFilePath;
        loadConfig();
    }
    
    @Override
    public TenantContext getTenantContext(String tenantId) {
        // 检查文件是否有更新
        checkAndReloadIfNeeded();
        return tenantContexts.get(tenantId);
    }
    
    @Override
    public List<String> getAllTenantIds() {
        checkAndReloadIfNeeded();
        return new ArrayList<>(tenantContexts.keySet());
    }
    
    @Override
    public boolean refresh() {
        try {
            loadConfig();
            notifyConfigRefreshed();
            return true;
        } catch (Exception e) {
            System.err.println("Failed to refresh tenant config: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public String getProviderType() {
        return "File-based (" + configFilePath + ")";
    }
    
    @Override
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    /**
     * 添加配置变更监听器
     */
    public void addChangeListener(TenantConfigChangeListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 移除配置变更监听器
     */
    public void removeChangeListener(TenantConfigChangeListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 检查文件是否有更新，如果有则重新加载
     */
    private void checkAndReloadIfNeeded() {
        File configFile = new File(configFilePath);
        if (configFile.exists() && configFile.lastModified() > lastFileModified) {
            refresh();
        }
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        File configFile = new File(configFilePath);
        if (!configFile.exists()) {
            System.out.println("Tenant config file not found: " + configFilePath + ", using default configuration");
            loadDefaultConfig();
            return;
        }
        
        try {
            if (configFilePath.endsWith(".json")) {
                loadJsonConfig(configFile);
            } else {
                loadPropertiesConfig(configFile);
            }
            
            lastFileModified = configFile.lastModified();
            lastUpdateTime = System.currentTimeMillis();
            
        } catch (IOException e) {
            System.err.println("Failed to load tenant config from " + configFilePath + ": " + e.getMessage());
            loadDefaultConfig();
        }
    }
    
    /**
     * 加载JSON格式配置
     */
    private void loadJsonConfig(File configFile) throws IOException {
        // 简单的JSON解析实现
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
        }
        
        parseJsonConfig(content.toString());
    }
    
    /**
     * 解析JSON配置
     */
    private void parseJsonConfig(String json) {
        Map<String, TenantContext> newContexts = new ConcurrentHashMap<>();
        
        // 简单的JSON解析 - 查找租户配置块
        // 格式: {"tenants": {"tenant1": {"secretKey": "key1", "basePath": "path1"}, ...}}
        
        String tenantsPattern = "\"tenants\"\\s*:\\s*\\{([^}]+)\\}";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(tenantsPattern);
        java.util.regex.Matcher matcher = pattern.matcher(json);
        
        if (matcher.find()) {
            String tenantsContent = matcher.group(1);
            
            // 解析每个租户配置
            String tenantPattern = "\"([^\"]+)\"\\s*:\\s*\\{([^}]+)\\}";
            pattern = java.util.regex.Pattern.compile(tenantPattern);
            matcher = pattern.matcher(tenantsContent);
            
            while (matcher.find()) {
                String tenantId = matcher.group(1);
                String tenantConfig = matcher.group(2);
                
                TenantContext context = parseTenantConfig(tenantConfig);
                if (context != null) {
                    newContexts.put(tenantId, context);
                }
            }
        }
        
        updateTenantContexts(newContexts);
    }
    
    /**
     * 加载Properties格式配置
     */
    private void loadPropertiesConfig(File configFile) throws IOException {
        Map<String, TenantContext> newContexts = new ConcurrentHashMap<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                
                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    String key = parts[0].trim();
                    String value = parts[1].trim();
                    
                    // 格式: tenant.{tenantId}.secretKey = value
                    if (key.startsWith("tenant.") && key.contains(".secretKey")) {
                        String tenantId = key.substring(7, key.lastIndexOf(".secretKey"));
                        
                        TenantContext context = newContexts.computeIfAbsent(tenantId, k -> new TenantContext());
                        context.setSecretKey(value);
                    }
                    // 格式: tenant.{tenantId}.basePath = value
                    else if (key.startsWith("tenant.") && key.contains(".basePath")) {
                        String tenantId = key.substring(7, key.lastIndexOf(".basePath"));
                        
                        TenantContext context = newContexts.computeIfAbsent(tenantId, k -> new TenantContext());
                        context.setBasePath(value);
                    }
                }
            }
        }
        
        updateTenantContexts(newContexts);
    }
    
    /**
     * 解析单个租户配置
     */
    private TenantContext parseTenantConfig(String config) {
        TenantContext context = new TenantContext();
        
        // 解析secretKey
        String secretKeyPattern = "\"secretKey\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(secretKeyPattern);
        java.util.regex.Matcher matcher = pattern.matcher(config);
        if (matcher.find()) {
            context.setSecretKey(matcher.group(1));
        }
        
        // 解析basePath
        String basePathPattern = "\"basePath\"\\s*:\\s*\"([^\"]+)\"";
        pattern = java.util.regex.Pattern.compile(basePathPattern);
        matcher = pattern.matcher(config);
        if (matcher.find()) {
            context.setBasePath(matcher.group(1));
        }
        
        return context.getSecretKey() != null ? context : null;
    }
    
    /**
     * 更新租户上下文并触发变更事件
     */
    private void updateTenantContexts(Map<String, TenantContext> newContexts) {
        Map<String, TenantContext> oldContexts = new ConcurrentHashMap<>(tenantContexts);
        
        // 检查新增和更新的租户
        for (Map.Entry<String, TenantContext> entry : newContexts.entrySet()) {
            String tenantId = entry.getKey();
            TenantContext newContext = entry.getValue();
            TenantContext oldContext = oldContexts.get(tenantId);
            
            if (oldContext == null) {
                // 新增租户
                notifyTenantAdded(tenantId, newContext);
            } else if (!oldContext.equals(newContext)) {
                // 更新租户
                notifyTenantUpdated(tenantId, oldContext, newContext);
            }
        }
        
        // 检查删除的租户
        for (Map.Entry<String, TenantContext> entry : oldContexts.entrySet()) {
            String tenantId = entry.getKey();
            if (!newContexts.containsKey(tenantId)) {
                notifyTenantRemoved(tenantId, entry.getValue());
            }
        }
        
        // 更新内存中的配置
        tenantContexts.clear();
        tenantContexts.putAll(newContexts);
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfig() {
        Map<String, TenantContext> defaultContexts = new ConcurrentHashMap<>();
        
        // 默认租户配置
        TenantContext defaultContext = new TenantContext();
        defaultContext.setSecretKey("default-secret-key-change-in-production");
        defaultContext.setBasePath("./files");
        defaultContexts.put("tenant1", defaultContext);
        
        TenantContext tenant2Context = new TenantContext();
        tenant2Context.setSecretKey("tenant2-secret-key");
        tenant2Context.setBasePath("./files/tenant2");
        defaultContexts.put("tenant2", tenant2Context);
        
        updateTenantContexts(defaultContexts);
        lastUpdateTime = System.currentTimeMillis();
    }
    
    // 事件通知方法
    private void notifyTenantAdded(String tenantId, TenantContext context) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantAdded(tenantId, context);
            } catch (Exception e) {
                System.err.println("Error notifying tenant added: " + e.getMessage());
            }
        }
    }
    
    private void notifyTenantUpdated(String tenantId, TenantContext oldContext, TenantContext newContext) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantUpdated(tenantId, oldContext, newContext);
            } catch (Exception e) {
                System.err.println("Error notifying tenant updated: " + e.getMessage());
            }
        }
    }
    
    private void notifyTenantRemoved(String tenantId, TenantContext context) {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onTenantRemoved(tenantId, context);
            } catch (Exception e) {
                System.err.println("Error notifying tenant removed: " + e.getMessage());
            }
        }
    }
    
    private void notifyConfigRefreshed() {
        for (TenantConfigChangeListener listener : listeners) {
            try {
                listener.onConfigRefreshed(this);
            } catch (Exception e) {
                System.err.println("Error notifying config refreshed: " + e.getMessage());
            }
        }
    }
}
