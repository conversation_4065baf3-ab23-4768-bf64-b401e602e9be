package com.sxr.integration.impl;

import com.sxr.file.FileHandler;
import com.sxr.file.FileRequest;
import com.sxr.file.FileResponse;
import com.sxr.integration.SecureFileRequest;
import com.sxr.integration.SecureFileService;
import com.sxr.integration.TenantContext;
import com.sxr.integration.plugin.RequestPlugin;
import com.sxr.integration.tenant.TenantConfigProvider;
import com.sxr.signature.SignRequest;
import com.sxr.signature.SignatureService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 默认安全文件服务实现
 * 整合签名验证和文件处理功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DefaultSecureFileService implements SecureFileService {

    private final SignatureService signatureService;
    private final FileHandler fileHandler;
    private final TenantConfigProvider tenantConfigProvider;
    private final List<RequestPlugin> plugins;

    public DefaultSecureFileService(SignatureService signatureService, FileHandler fileHandler, TenantConfigProvider tenantConfigProvider) {
        this.signatureService = signatureService;
        this.fileHandler = fileHandler;
        this.tenantConfigProvider = tenantConfigProvider;
        this.plugins = new ArrayList<>();
    }

    // 兼容性构造函数，使用默认的内存配置提供者
    public DefaultSecureFileService(SignatureService signatureService, FileHandler fileHandler) {
        this(signatureService, fileHandler, new com.sxr.integration.tenant.impl.MemoryTenantConfigProvider());
    }

    @Override
    public FileResponse handleSecureDownload(SecureFileRequest request) {
        return handleSecureRequest(request, false);
    }

    @Override
    public FileResponse handleSecurePreview(SecureFileRequest request) {
        return handleSecureRequest(request, true);
    }

    @Override
    public String generateSecureUrl(SecureFileRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("SecureFileRequest cannot be null");
        }

        // 获取租户上下文
        TenantContext tenantContext = getTenantContext(request.getTenantId());
        if (tenantContext == null) {
            throw new IllegalArgumentException("Tenant not found: " + request.getTenantId());
        }

        // 构建签名请求
        SignRequest signRequest = buildSignRequest(request);
        signRequest.setSecretKey(tenantContext.getSecretKey()); // 设置密钥

        // 生成签名
        String signature = signatureService.generateSignature(signRequest);

        // 构建安全URL
        return buildSecureUrl(request, signature);
    }

    /**
     * 处理安全请求的通用方法
     */
    private FileResponse handleSecureRequest(SecureFileRequest request, boolean isPreview) {
        if (request == null) {
            return FileResponse.error(400, "Invalid request");
        }

        try {
            // 获取租户上下文
            TenantContext tenantContext = getTenantContext(request.getTenantId());
            if (tenantContext == null) {
                return FileResponse.error(404, "Tenant not found");
            }

            // 执行插件预处理
            if (!executePrePlugins(request, tenantContext)) {
                return FileResponse.forbidden();
            }

            // 检查是否过期
            if (isExpired(request)) {
                return FileResponse.error(410, "Request expired");
            }

            // 检查文件扩展名
            if (!isExtensionAllowed(request, tenantContext)) {
                return FileResponse.error(403, "File type not allowed");
            }


            // 验证签名
            if (!verifySignature(request, tenantContext)) {
                return FileResponse.error(401, "Invalid signature");
            }


            // 构建文件请求
            FileRequest fileRequest = buildFileRequest(request, isPreview);

            // 处理文件请求
            FileResponse response;
            if (isPreview) {
                response = fileHandler.handlePreview(fileRequest);
            } else {
                response = fileHandler.handleDownload(fileRequest);
            }

            // 执行插件后处理
            executePostPlugins(request, response, tenantContext);

            return response;

        } catch (Exception e) {
            return FileResponse.internalError("Failed to process request: " + e.getMessage());
        }
    }

    /**
     * 验证签名
     */
    private boolean verifySignature(SecureFileRequest request, TenantContext tenantContext) {
        if (request.getSignature() == null) {
            return false;
        }

        // 构建签名请求
        SignRequest signRequest = buildSignRequest(request);
        signRequest.setSecretKey(tenantContext.getSecretKey());

        // 重新生成签名进行比较
        String expectedSignature = signatureService.generateSignature(signRequest);
        return request.getSignature().equals(expectedSignature);
    }

    /**
     * 检查是否过期
     */
    private boolean isExpired(SecureFileRequest request) {
        if (request.getExpireTime() == null) {
            return false;
        }
        return System.currentTimeMillis() > request.getExpireTime();
    }

    /**
     * 检查文件扩展名是否被允许
     */
    private boolean isExtensionAllowed(SecureFileRequest request, TenantContext tenantContext) {
        String extension = request.getFileExtension();
        if (extension == null) {
            return true; // 没有扩展名，默认允许
        }

        if (tenantContext.getConfig() != null) {
            return tenantContext.getConfig().isExtensionAllowed(extension);
        }

        return true; // 没有配置，默认允许
    }

    /**
     * 构建签名请求
     */
    private SignRequest buildSignRequest(SecureFileRequest request) {
        SignRequest signRequest = new SignRequest();
        signRequest.setTenantId(request.getTenantId());
        signRequest.setFilePath(request.getFilePath());
        signRequest.setExpireTime(request.getExpireTime());
        signRequest.setAccessLimit(request.getAccessLimit());
        // 注意：clientIp不参与签名计算，避免用户切换网络时签名失效
        // signRequest.setClientIp(request.getClientIp());
        signRequest.setExtraParams(request.getExtraParams());
        return signRequest;
    }

    /**
     * 构建文件请求
     */
    private FileRequest buildFileRequest(SecureFileRequest request, boolean isPreview) {
        FileRequest fileRequest = new FileRequest();
        fileRequest.setTenantId(request.getTenantId());
        fileRequest.setFilePath(request.getFilePath());
        fileRequest.setFileName(request.getFileName());
        fileRequest.setPreview(isPreview);
        fileRequest.setRangeHeader(request.getRangeHeader());
        fileRequest.setClientIp(request.getClientIp());
        fileRequest.setUserAgent(request.getUserAgent());
        fileRequest.setHeaders(request.getHeaders());
        return fileRequest;
    }

    /**
     * 构建安全URL
     */
    private String buildSecureUrl(SecureFileRequest request, String signature) {
        StringBuilder urlBuilder = new StringBuilder();

        // 基础路径 - 使用正确的SXR路径
        urlBuilder.append("/sxr/");
        if (request.isPreview()) {
            urlBuilder.append("preview/");
        } else {
            urlBuilder.append("download/");
        }

        // 租户ID
        if (request.getTenantId() != null) {
            urlBuilder.append(request.getTenantId()).append("/");
        }

        // 文件路径
        urlBuilder.append(request.getFilePath());

        // 查询参数 - 对签名进行URL编码
        urlBuilder.append("?signature=").append(urlEncode(signature));

        if (request.getExpireTime() != null) {
            urlBuilder.append("&expire=").append(request.getExpireTime());
        }

        if (request.getAccessLimit() != null) {
            urlBuilder.append("&limit=").append(request.getAccessLimit());
        }

        // 添加extraParams到URL中
        if (request.getExtraParams() != null && !request.getExtraParams().isEmpty()) {
            for (Map.Entry<String, String> entry : request.getExtraParams().entrySet()) {
                urlBuilder.append("&").append(urlEncode(entry.getKey()))
                          .append("=").append(urlEncode(entry.getValue()));
            }
        }

        return urlBuilder.toString();
    }

    /**
     * URL编码 - 使用JDK自带的URLEncoder
     */
    private String urlEncode(String value) {
        if (value == null) {
            return "";
        }
        try {
            return java.net.URLEncoder.encode(value, "UTF-8");
        } catch (java.io.UnsupportedEncodingException e) {
            // UTF-8是标准编码，不会出现此异常
            throw new RuntimeException("UTF-8 encoding not supported", e);
        }
    }

    /**
     * 获取租户上下文
     * 使用配置提供者获取租户信息，支持动态配置
     */
    private TenantContext getTenantContext(String tenantId) {
        return tenantConfigProvider.getTenantContext(tenantId);
    }

    /**
     * 执行插件预处理
     */
    private boolean executePrePlugins(SecureFileRequest request, TenantContext context) {
        for (RequestPlugin plugin : plugins) {
            try {
                if (!plugin.preHandle(request, context)) {
                    return false;
                }
            } catch (Exception e) {
                // 插件异常不应该影响主流程，记录日志即可
                System.err.println("Plugin " + plugin.getName() + " preHandle failed: " + e.getMessage());
            }
        }
        return true;
    }

    /**
     * 执行插件后处理
     */
    private void executePostPlugins(SecureFileRequest request, FileResponse response, TenantContext context) {
        for (RequestPlugin plugin : plugins) {
            try {
                plugin.postHandle(request, response, context);
            } catch (Exception e) {
                // 插件异常不应该影响主流程，记录日志即可
                System.err.println("Plugin " + plugin.getName() + " postHandle failed: " + e.getMessage());
            }
        }
    }

    /**
     * 添加插件
     */
    public void addPlugin(RequestPlugin plugin) {
        if (plugin != null) {
            plugins.add(plugin);
            // 按优先级排序
            Collections.sort(plugins, Comparator.comparingInt(RequestPlugin::getOrder));
        }
    }

    /**
     * 移除插件
     */
    public void removePlugin(String pluginName) {
        plugins.removeIf(plugin -> plugin.getName().equals(pluginName));
    }
}
