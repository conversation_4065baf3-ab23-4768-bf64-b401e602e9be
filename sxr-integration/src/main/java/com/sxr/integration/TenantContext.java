package com.sxr.integration;

/**
 * 租户上下文
 * 存储当前请求的租户信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TenantContext {
    
    /** 租户ID */
    private String tenantId;
    
    /** 租户名称 */
    private String tenantName;
    
    /** 租户密钥 */
    private String secretKey;
    
    /** 租户配置 */
    private TenantConfig config;
    
    public TenantContext() {
    }
    
    public TenantContext(String tenantId) {
        this.tenantId = tenantId;
    }
    
    // Getter and Setter methods
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getTenantName() {
        return tenantName;
    }
    
    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }
    
    public String getSecretKey() {
        return secretKey;
    }
    
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
    
    public TenantConfig getConfig() {
        return config;
    }
    
    public void setConfig(TenantConfig config) {
        this.config = config;
    }
    
    @Override
    public String toString() {
        return "TenantContext{" +
                "tenantId='" + tenantId + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", secretKey='***'" +
                '}';
    }
}
