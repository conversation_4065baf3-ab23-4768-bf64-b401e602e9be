# Bug修复验证

## 问题描述
用户报告的错误：`{"success":false,"error":"SignRequest and secretKey cannot be null"}`

## 根本原因
在`DefaultSecureFileService.generateSecureUrl()`方法中，构建`SignRequest`时没有设置`secretKey`。

## 修复内容

### 1. 修复前的代码
```java
@Override
public String generateSecureUrl(SecureFileRequest request) {
    // 构建签名请求
    SignRequest signRequest = buildSignRequest(request);
    
    // 生成签名 - 这里会失败，因为没有secretKey
    String signature = signatureService.generateSignature(signRequest);
    
    return buildSecureUrl(request, signature);
}
```

### 2. 修复后的代码
```java
@Override
public String generateSecureUrl(SecureFileRequest request) {
    if (request == null) {
        throw new IllegalArgumentException("SecureFileRequest cannot be null");
    }
    
    // 获取租户上下文
    TenantContext tenantContext = getTenantContext(request.getTenantId());
    if (tenantContext == null) {
        throw new IllegalArgumentException("Tenant not found: " + request.getTenantId());
    }
    
    // 构建签名请求
    SignRequest signRequest = buildSignRequest(request);
    signRequest.setSecretKey(tenantContext.getSecretKey()); // 🔧 修复：设置密钥
    
    // 生成签名
    String signature = signatureService.generateSignature(signRequest);
    
    // 构建安全URL
    return buildSecureUrl(request, signature);
}
```

### 3. 同时修复URL路径
将`/secure/`改为`/sxr/`以匹配控制器路径。

## 测试验证

### 测试用例
```bash
curl -X POST http://localhost:8080/sxr/generate-url \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "tenant1",
    "filePath": "images/test.jpg",
    "fileName": "test.jpg",
    "expireTime": 1703980800000,
    "preview": true
  }'
```

### 预期结果
```json
{
  "success": true,
  "url": "/sxr/preview/tenant1/images/test.jpg?signature=xxx&expire=1703980800000"
}
```

## 修复验证
- ✅ 编译成功
- ✅ 单元测试通过
- ✅ 集成测试通过
- ⏳ API测试待验证

## 相关文件
- `sxr-integration/src/main/java/com/sxr/integration/impl/DefaultSecureFileService.java`
