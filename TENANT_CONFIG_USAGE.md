# 多租户配置系统使用指南

## 🎯 概述

新的多租户配置系统提供了灵活的配置管理方案，支持：
- **动态配置**：无需重启服务即可更新配置
- **多种配置源**：文件、内存、HTTP接口等
- **热更新**：实时监听配置变更
- **事件通知**：配置变更时触发回调

## 🏗️ 架构设计

### 核心接口
```java
TenantConfigProvider - 配置提供者接口
TenantConfigChangeListener - 配置变更监听器
```

### 实现类
- `MemoryTenantConfigProvider` - 内存配置（测试/简单场景）
- `FileTenantConfigProvider` - 文件配置（生产推荐）
- `HttpTenantConfigProvider` - HTTP接口配置（微服务场景）

## 📝 使用方式

### 1. 基于文件的配置

#### JSON格式配置文件 (tenant-config.json)
```json
{
  "tenants": {
    "tenant1": {
      "secretKey": "tenant1-secret-key-change-in-production",
      "basePath": "./files/tenant1"
    },
    "tenant2": {
      "secretKey": "tenant2-secret-key-very-secure",
      "basePath": "./files/tenant2"
    }
  }
}
```

#### Properties格式配置文件 (tenant-config.properties)
```properties
# 租户1配置
tenant.tenant1.secretKey = tenant1-secret-key-change-in-production
tenant.tenant1.basePath = ./files/tenant1

# 租户2配置
tenant.tenant2.secretKey = tenant2-secret-key-very-secure
tenant.tenant2.basePath = ./files/tenant2
```

#### 使用示例
```java
// 创建文件配置提供者
TenantConfigProvider provider = new FileTenantConfigProvider("tenant-config.json");

// 创建服务实例
DefaultSecureFileService service = new DefaultSecureFileService(
    signatureService, 
    fileHandler, 
    provider
);
```

### 2. 基于内存的配置

```java
// 创建内存配置提供者
MemoryTenantConfigProvider provider = new MemoryTenantConfigProvider();

// 添加租户配置
TenantContext tenant = new TenantContext();
tenant.setSecretKey("my-secret-key");
tenant.setBasePath("./files/my-tenant");
provider.putTenantContext("myTenant", tenant);

// 创建服务实例
DefaultSecureFileService service = new DefaultSecureFileService(
    signatureService, 
    fileHandler, 
    provider
);
```

### 3. 基于HTTP接口的配置

```java
// 创建HTTP配置提供者（每60秒刷新一次）
TenantConfigProvider provider = new HttpTenantConfigProvider(
    "http://config-server/api/tenants", 
    60
);

// 创建服务实例
DefaultSecureFileService service = new DefaultSecureFileService(
    signatureService, 
    fileHandler, 
    provider
);
```

#### HTTP接口返回格式
```json
{
  "tenants": {
    "tenant1": {
      "secretKey": "tenant1-key",
      "basePath": "/data/tenant1"
    },
    "tenant2": {
      "secretKey": "tenant2-key", 
      "basePath": "/data/tenant2"
    }
  }
}
```

## 🔄 热更新功能

### 配置变更监听
```java
// 添加配置变更监听器
provider.addChangeListener(new TenantConfigChangeListener() {
    @Override
    public void onTenantAdded(String tenantId, TenantContext context) {
        System.out.println("新增租户: " + tenantId);
    }
    
    @Override
    public void onTenantUpdated(String tenantId, TenantContext oldContext, TenantContext newContext) {
        System.out.println("更新租户: " + tenantId);
        // 可以在这里清理缓存、重新加载资源等
    }
    
    @Override
    public void onTenantRemoved(String tenantId, TenantContext context) {
        System.out.println("删除租户: " + tenantId);
    }
    
    @Override
    public void onConfigRefreshed(TenantConfigProvider provider) {
        System.out.println("配置已刷新: " + provider.getProviderType());
    }
});
```

### 手动刷新配置
```java
// 手动触发配置刷新
boolean success = provider.refresh();
if (success) {
    System.out.println("配置刷新成功");
} else {
    System.out.println("配置刷新失败");
}
```

## 🚀 Spring Boot集成

### 自动配置
```java
@Configuration
public class TenantConfigConfiguration {
    
    @Bean
    @ConditionalOnProperty(name = "sxr.tenant.config.type", havingValue = "file")
    public TenantConfigProvider fileTenantConfigProvider(
            @Value("${sxr.tenant.config.file-path:tenant-config.json}") String filePath) {
        return new FileTenantConfigProvider(filePath);
    }
    
    @Bean
    @ConditionalOnProperty(name = "sxr.tenant.config.type", havingValue = "http")
    public TenantConfigProvider httpTenantConfigProvider(
            @Value("${sxr.tenant.config.url}") String configUrl,
            @Value("${sxr.tenant.config.refresh-interval:60}") int refreshInterval) {
        return new HttpTenantConfigProvider(configUrl, refreshInterval);
    }
    
    @Bean
    @ConditionalOnMissingBean
    public TenantConfigProvider defaultTenantConfigProvider() {
        return new MemoryTenantConfigProvider();
    }
}
```

### 配置文件 (application.yml)
```yaml
sxr:
  tenant:
    config:
      type: file  # file, http, memory
      file-path: ./config/tenant-config.json
      # url: http://config-server/api/tenants
      # refresh-interval: 60
```

## 🔧 高级功能

### 自定义配置提供者
```java
public class DatabaseTenantConfigProvider implements TenantConfigProvider {
    
    private final DataSource dataSource;
    
    @Override
    public TenantContext getTenantContext(String tenantId) {
        // 从数据库查询租户配置
        return queryTenantFromDatabase(tenantId);
    }
    
    @Override
    public boolean refresh() {
        // 刷新数据库连接或缓存
        return true;
    }
    
    // 实现其他方法...
}
```

### 配置缓存
```java
public class CachedTenantConfigProvider implements TenantConfigProvider {
    
    private final TenantConfigProvider delegate;
    private final Map<String, TenantContext> cache = new ConcurrentHashMap<>();
    private volatile long lastRefreshTime = 0;
    private final long cacheExpireTime = 300000; // 5分钟
    
    @Override
    public TenantContext getTenantContext(String tenantId) {
        if (System.currentTimeMillis() - lastRefreshTime > cacheExpireTime) {
            refresh();
        }
        return cache.computeIfAbsent(tenantId, delegate::getTenantContext);
    }
    
    // 实现其他方法...
}
```

## 📊 监控和运维

### 配置状态查询
```java
// 获取配置提供者类型
String providerType = provider.getProviderType();

// 获取最后更新时间
long lastUpdate = provider.getLastUpdateTime();

// 获取所有租户列表
List<String> tenantIds = provider.getAllTenantIds();

// 检查租户是否存在
boolean exists = provider.tenantExists("tenant1");
```

### 健康检查
```java
@Component
public class TenantConfigHealthIndicator implements HealthIndicator {
    
    private final TenantConfigProvider provider;
    
    @Override
    public Health health() {
        try {
            List<String> tenants = provider.getAllTenantIds();
            return Health.up()
                    .withDetail("provider", provider.getProviderType())
                    .withDetail("tenantCount", tenants.size())
                    .withDetail("lastUpdate", new Date(provider.getLastUpdateTime()))
                    .build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}
```

## 🎯 最佳实践

1. **生产环境推荐使用文件配置**，便于版本控制和部署
2. **微服务环境可使用HTTP配置**，实现集中化配置管理
3. **添加配置变更监听器**，及时响应配置变化
4. **定期备份配置文件**，防止配置丢失
5. **使用强密钥**，确保签名安全性
6. **监控配置刷新状态**，及时发现配置问题

## 🔒 安全注意事项

1. **密钥管理**：使用环境变量或密钥管理系统存储敏感信息
2. **文件权限**：确保配置文件只有应用程序可读
3. **网络安全**：HTTP配置接口应使用HTTPS和认证
4. **日志安全**：避免在日志中输出敏感配置信息

这个多租户配置系统为您提供了灵活、可扩展的配置管理方案，支持各种部署场景和运维需求！
