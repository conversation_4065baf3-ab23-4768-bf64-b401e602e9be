# SXR文件签名系统 - 项目结构

## 项目概览

本项目采用多模块Maven架构，严格按照四层分离设计，支持JDK8，零第三方依赖的核心模块，具备良好的插拔性和扩展性。

## 模块结构

```
sxr-file-signature/
├── pom.xml                           # 父项目POM
├── README.md                         # 项目说明文档
├── USAGE.md                          # 使用指南
├── PROJECT_STRUCTURE.md              # 项目结构说明
│
├── sxr-signature-core/               # 签名核心模块（独立）
│   ├── pom.xml
│   └── src/
│       ├── main/java/com/sxr/signature/
│       │   ├── SignatureService.java           # 签名服务接口
│       │   ├── SignRequest.java                # 签名请求参数
│       │   └── impl/
│       │       └── HmacSignatureService.java   # HMAC签名实现
│       └── test/java/com/sxr/signature/
│           └── impl/
│               └── HmacSignatureServiceTest.java
│
├── sxr-file-handler/                 # 文件处理模块（独立）
│   ├── pom.xml
│   └── src/
│       ├── main/java/com/sxr/file/
│       │   ├── FileHandler.java                # 文件处理器接口
│       │   ├── FileRequest.java                # 文件请求参数
│       │   ├── FileResponse.java               # 文件响应结果
│       │   └── impl/
│       │       └── DefaultFileHandler.java     # 默认文件处理器
│       └── test/java/com/sxr/file/
│           └── impl/
│               └── DefaultFileHandlerTest.java
│
├── sxr-integration/                  # 整合模块
│   ├── pom.xml
│   └── src/
│       └── main/java/com/sxr/integration/
│           ├── SecureFileService.java          # 安全文件服务接口
│           ├── SecureFileRequest.java          # 安全文件请求参数
│           ├── TenantContext.java              # 租户上下文
│           ├── TenantConfig.java               # 租户配置
│           ├── plugin/
│           │   └── RequestPlugin.java          # 请求插件接口
│           └── impl/
│               └── DefaultSecureFileService.java # 默认安全文件服务
│
└── sxr-spring-boot-starter/          # Spring Boot启动器
    ├── pom.xml
    └── src/
        ├── main/
        │   ├── java/com/sxr/spring/
        │   │   ├── SxrFileSignatureApplication.java      # 启动类
        │   │   ├── SxrFileSignatureAutoConfiguration.java # 自动配置
        │   │   ├── SxrFileSignatureProperties.java       # 配置属性
        │   │   └── web/
        │   │       └── SecureFileController.java         # Web控制器
        │   └── resources/
        │       ├── META-INF/
        │       │   └── spring.factories                  # Spring Boot自动配置
        │       └── application.yml                       # 示例配置
        └── test/
```

## 模块依赖关系

```
sxr-spring-boot-starter
    └── sxr-integration
            ├── sxr-signature-core (独立，零依赖)
            └── sxr-file-handler (独立，零依赖)
```

## 核心特性

### 1. 四层架构分离
- **签名模块**: 独立的URL签名生成和验证
- **文件处理模块**: 独立的文件下载和预览处理
- **整合模块**: 组合上述功能，提供完整业务逻辑
- **Spring Boot容器**: 快速启动和Web接口

### 2. 多租户支持
- 域名路由模式: `tenant1.domain.com`
- 路径前缀模式: `/tenant1/xxx`
- 租户配置隔离: 独立密钥、存储路径、访问策略

### 3. JDK8兼容
- 全面支持JDK8语法和API
- 核心模块零第三方依赖
- 使用Spring Boot 2.7.x（兼容JDK8）

### 4. 插拔式设计
- 接口驱动的架构设计
- 支持自定义签名算法
- 支持自定义文件处理器
- 支持插件扩展机制

## 技术栈

### 核心技术
- **JDK**: 8+
- **构建工具**: Maven 3.6+
- **Web框架**: Spring Boot 2.7.x
- **签名算法**: HMAC-SHA256, RSA
- **文件处理**: Java NIO

### 测试框架
- **单元测试**: JUnit 4
- **集成测试**: Spring Boot Test

## 配置说明

### Maven配置
- 统一版本管理
- JDK8编译配置
- 依赖管理和版本控制

### Spring Boot配置
- 自动配置机制
- 外部化配置支持
- 多环境配置

### 多租户配置
- 灵活的租户识别模式
- 租户级别的个性化配置
- 安全的密钥管理

## 扩展点

### 1. 签名算法扩展
实现 `SignatureService` 接口，支持自定义签名算法

### 2. 文件处理扩展
实现 `FileHandler` 接口，支持自定义文件处理逻辑

### 3. 插件扩展
实现 `RequestPlugin` 接口，支持请求处理的扩展

### 4. 存储扩展
可扩展支持对象存储（S3、OSS等）

## 安全特性

1. **签名验证**: 基于密钥的URL签名机制
2. **时效控制**: 支持URL过期时间
3. **访问限制**: 支持访问次数限制
4. **文件类型控制**: 支持文件扩展名白名单/黑名单
5. **多租户隔离**: 租户级别的安全隔离

## 性能特性

1. **流式处理**: 大文件分块传输，内存友好
2. **断点续传**: 支持HTTP Range请求
3. **缓存控制**: 合理的HTTP缓存策略
4. **线程安全**: 无状态设计，支持高并发

## 部署方式

1. **独立部署**: 直接运行Spring Boot应用
2. **容器部署**: 支持Docker容器化
3. **集群部署**: 支持负载均衡和水平扩展
4. **云原生**: 支持Kubernetes部署

## 监控和运维

1. **健康检查**: Spring Boot Actuator集成
2. **指标监控**: 支持Micrometer指标
3. **日志记录**: 结构化日志输出
4. **配置管理**: 外部化配置支持
